<template>
  <van-dialog 
    v-model="showDialog" 
    className="withdraw-limited-dialog" 
    :closeOnClickOverlay="false" 
    :showConfirmButton="false"
    :showCancelButton="false"
  >
    <div class="dialog-content">
      <!-- 关闭按钮 -->
      <img 
        class="close-btn" 
        src="@/assets/images/common/close.png" 
        @click="handleClose" 
        alt="Close"
      >
      
      <!-- 主要内容 -->
      <div class="content-wrapper">
        <div class="title">Withdrawal Limited</div>
        <div class="description">
          Your withdrawal is currently limited. Please change your bank account or contact your bank for assistance.
        </div>
        
        <!-- 按钮区域 -->
        <div class="button-group">
          <div class="change-bank-btn" @click="handleChangeBankAccount">
            Change Bank Account
          </div>
          <div 
            v-if="showContactBankButton" 
            class="contact-bank-btn" 
            @click="handleContactBank"
          >
            Go to Contact Bank
          </div>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'WithdrawLimitedDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    showContactBankButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showDialog: false
    }
  },
  watch: {
    show: {
      handler(newVal) {
        this.showDialog = newVal;
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.showDialog = false;
      this.$emit('close');
    },
    handleChangeBankAccount() {
      this.showDialog = false;
      this.$emit('close');
      this.$emit('changeBankAccount');
    },
    handleContactBank() {
      this.showDialog = false;
      this.$emit('close');
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .withdraw-limited-dialog {
  .van-dialog {
    border-radius: 16px;
    padding: 0;
    overflow: hidden;
  }
  
  .van-dialog__content {
    padding: 0;
  }
}

.dialog-content {
  position: relative;
  padding: 24px 20px 20px;
  background: #ffffff;
  border-radius: 16px;
  
  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 10;
  }
  
  .content-wrapper {
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #1B3155;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 8px;
    }
    
    .description {
      font-size: 14px;
      color: #666666;
      text-align: center;
      line-height: 20px;
      margin-bottom: 24px;
      padding: 0 8px;
    }
    
    .button-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .change-bank-btn {
        height: 44px;
        background: #FF8800;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
        cursor: pointer;
        transition: background-color 0.3s;
        
        &:active {
          background: #e67700;
        }
      }
      
      .contact-bank-btn {
        height: 44px;
        background: #ffffff;
        border: 1px solid #E0E0E0;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
        color: #666666;
        cursor: pointer;
        transition: all 0.3s;
        
        &:active {
          background: #f5f5f5;
          border-color: #d0d0d0;
        }
      }
    }
  }
}
</style>
