<template>
  <van-dialog
    v-model="showDialog"
    className="withdraw-limited-dialog"
    :closeOnClickOverlay="false"
    :showConfirmButton="false"
    :showCancelButton="false"
  >
    <div class="dialog-content">
      <!-- 关闭按钮 -->
      <img
        class="close-btn"
        src="@/assets/images/common/close.png"
        @click="handleClose"
        alt="Close"
      >

      <!-- 主要内容 -->
      <div class="content-wrapper">
        <div class="title">Withdrawal Failed Due to Bank Account Limit</div>
        <div class="description">
          Your last withdrawal failed because your bank account has transaction limits. Please change to another bank account or request limit increase from your bank [XXX bank (9898)], and then resubmit the withdrawal.
        </div>

        <!-- 按钮区域 -->
        <div class="button-group">
          <div class="change-bank-btn" @click="handleChangeBankAccount">
            Change Bank Account
          </div>
          <div
            v-if="showContactBankButton"
            class="contact-bank-btn"
            @click="handleContactBank"
          >
            Go to Contact Bank
          </div>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'WithdrawLimitedDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    showContactBankButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showDialog: false
    }
  },
  watch: {
    show: {
      handler(newVal) {
        this.showDialog = newVal;
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.showDialog = false;
      this.$emit('close');
    },
    handleChangeBankAccount() {
      this.showDialog = false;
      this.$emit('close');
      this.$emit('changeBankAccount');
    },
    handleContactBank() {
      this.showDialog = false;
      this.$emit('close');
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .withdraw-limited-dialog {
  .van-dialog {
    border-radius: 24px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0px 2px 20px 0px rgba(0, 54, 101, 0.06);
  }

  .van-dialog__content {
    padding: 0;
  }
}

.dialog-content {
  position: relative;
  padding: 40px 54px 64px;
  background: #ffffff;
  border-radius: 24px;
  width: 608px;
  max-width: 90vw;

  .close-btn {
    position: absolute;
    top: 49px;
    right: 49px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 10;
    opacity: 0.6;
  }

  .content-wrapper {
    .title {
      font-family: Avenir, sans-serif;
      font-size: 32px;
      font-weight: 800;
      color: #000000;
      text-align: left;
      line-height: 1.4375em;
      margin-bottom: 32px;
      margin-top: 0;
    }

    .description {
      font-family: Avenir, sans-serif;
      font-size: 28px;
      font-weight: 500;
      color: #536887;
      text-align: left;
      line-height: 1.6em;
      margin-bottom: 70px;
      padding: 0;
    }

    .button-group {
      display: flex;
      flex-direction: column;
      gap: 22px;

      .change-bank-btn {
        height: 84px;
        background: #099BFA;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Avenir, sans-serif;
        font-size: 36px;
        font-weight: 800;
        color: #ffffff;
        cursor: pointer;
        transition: background-color 0.3s;
        line-height: 1.366em;

        &:active {
          background: #0888e6;
        }
      }

      .contact-bank-btn {
        height: 38px;
        background: transparent;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Avenir, sans-serif;
        font-size: 28px;
        font-weight: 500;
        color: #919DB3;
        cursor: pointer;
        transition: all 0.3s;
        line-height: 1.366em;

        &:active {
          color: #7a8ba3;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .dialog-content {
    padding: 20px 27px 32px !important;
    width: auto !important;
    max-width: 85vw !important;

    .content-wrapper {
      .title {
        font-size: 16px !important;
        margin-bottom: 16px !important;
      }

      .description {
        font-size: 14px !important;
        margin-bottom: 35px !important;
      }

      .button-group {
        gap: 11px !important;

        .change-bank-btn {
          height: 42px !important;
          font-size: 18px !important;
          border-radius: 8px !important;
        }

        .contact-bank-btn {
          height: 19px !important;
          font-size: 14px !important;
        }
      }
    }

    .close-btn {
      top: 25px !important;
      right: 25px !important;
      width: 12px !important;
      height: 12px !important;
    }
  }
}
</style>
