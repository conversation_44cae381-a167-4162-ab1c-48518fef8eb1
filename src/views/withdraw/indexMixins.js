import backImg from '@/assets/images/UBA.png';
import ninBankImg from '@/assets/images/nin.png';
import goldManImg from '@/assets/images/goldMan.png';
import pick from '@/assets/images/pick.png';
import arrowDown from '@/assets/images/arrow-down.png';
import addBankIcon from '@/assets/images/add-bank.png';
import unchecked from '@/assets/images/un-paid.png';
import withdrawAlert from '@/assets/images/withdraw-alert.png';
import cancel from '@/assets/images/cancel2.png';
import CPage from '@/components/c-page.vue';
import CButton from '@/components/c-button.vue';
import CMoney from '@/components/c-money.vue';
import CPopover from '@/components/c-popover.vue';
import loanTerms from '@/components/loan-terms.vue';
import loanTermsWayacredit from '@/components/loan-terms-wayacredit.vue';
import CDialog from '@/components/c-dialog.vue';
import faqGoldman from '@/components/faq-goldman.vue';
import addPersonalInformation from '@/components/addPersonalInformation.vue';
import NinCheck from './components/nin-check.vue';
// import backPopup from './components/back-popup.vue';
import giftPopup from './components/gift-popup.vue';
import reasonPopup from './components/reason-popup.vue';
import giftPopupWayacredit from './components/gift-popup-wayacredit.vue';
import faqPopup from './components/faq-popup.vue';
import repaymentDetailPopup from './components/repayment-detail-popup.vue';
import repaymentPlanWayacredit from './components/repayment-plan-wayacredit.vue';
import Question from './components/question.vue';
import tips from '@/assets/images/tips.png';
import { guid, dateFormat, judgeClient, debounce, inTimeAreaCal } from '@/assets/js/common';
import { Popup } from 'vant';
import banner1 from '@/assets/images/common/banner1.png';
import banner2 from '@/assets/images/common/banner2.png';
import banner3 from '@/assets/images/common/banner3.png';
import banner1replace from '@/assets/images/common/banner1replace.png';
import { existedPin, getAesString, getGpsInfo, getWifiList, getDeviceId, gotoHomeActivity, getCustId, getCurrentAppVersionName, getCurrentAppVersion,
  activateTradeBigdata, activateTradeBigdataAC, gotoBindBankAccount,  gotoBindBankCard, applyAppsflyerData, getLocation, getBatchNo, uploadAdsEvent, log, getOsVersionCode } from "@/assets/js/native";
import { mapState } from 'vuex';
import api from "@/api/interface";
import GiftDialog from './components/gift-dialog.vue';

export default {
  name: 'index',
  components: {
    Popup,
    CPage,
    CButton,
    CMoney,
    Question,
    loanTerms,
    loanTermsWayacredit,
    CPopover,
    CDialog,
    NinCheck,
    // backPopup,
    giftPopup,
    reasonPopup,
    faqPopup,
    faqGoldman,
    GiftDialog,
    repaymentDetailPopup,
    repaymentPlanWayacredit,
    giftPopupWayacredit,
    addPersonalInformation
  },
  computed: {
    ...mapState(['coupon', 'deviceType', 'userInfor', 'uid', 'addHeader', 'productSource', 'channel', 'nodeCode',
      'acWithdrawSubmitData', 'supportedFeatures', 'uploadDocuments', 'needReCreditContractList', 'needReCreditliveDetection', 'checkDeviceStage', 'checkDeviceStageResult']),
    installAmt () {
      let vm = this;
      console.log('vm.longActive', vm.longActive);
      if (vm.longActive > -1) {
        let product = vm.loanDetail.productList[vm.longActive];
        let productFee = product.productFee[0];
        return productFee.differenceLmtAmt ? productFee.differenceLmtAmt : 0;
      } else {
        return 0
      }
    },
    indexProduct() {
      let vm = this;
      let indexProduct = {};
      if (vm.longActive > -1) {
        indexProduct = vm.loanDetail.productList[vm.longActive];
      }
      console.log('indexProduct', indexProduct);
      return indexProduct;
    },
    totalDue() {
      let vm = this;
      // 减免之前的金额
      let totalAmountDue = vm.loanDetail.repayment.totalAmountDue || 0;
      // 减免后的金额
      let outstdBal = vm.loanDetail.repayment.outstdBal || 0;
      return {
        totalAmountDue,
        outstdBal,
        // 差值
        interestRelief: vm.common.floatMinus(totalAmountDue, outstdBal).toString(),
      };
    },
    rangeTips() {
      return `Cash withdrawal range: ₦${this.common.singleToThousands(this.chooseTypeData.minAmount)}-₦${this.common.singleToThousands(this.chooseTypeData.maxAmount)}`
    },
    // 提额降息券信息
    useScene5Coupon() {
      let limitIncrease = 0;
      let interestRelief = 0;
      const product = this.loanDetail.productList[this.longActive];
      if (product) {
        const beforeProduct =  this.loanDetail.temProductList.find(item => item.productId === product.productId);
        console.log('product', product, 'beforeProduct', beforeProduct, 'coupon', JSON.stringify(this.coupon));
        if (this.coupon.useScene === '5' && product && product.productFee && beforeProduct && beforeProduct.productFee) {
          // 这里前端不需要考虑提额是比例还是金额。因为提额计算逻辑是由后台计算的，前端只要计算提额前后同个产品的最大值即是提额真实值。
          limitIncrease = product.productFee[0].maxAmt - beforeProduct.productFee[0].maxAmt;
          // 目前没有针对多期的利息减免，先展示优惠券的优惠金额
          if (this.coupon.couponMethod === 'A') {
            interestRelief = this.coupon.denominations;
          } else if (this.coupon.couponMethod === 'R') {
            interestRelief = this.coupon.denominations * this.loanDetail.repayment.interest;
          }
        }
      }
      console.log('limitIncrease', limitIncrease, 'interestRelief', interestRelief)
      return {
        limitIncrease,
        interestRelief
      }
    },
    bottomText() {
      if (this.isNew && this.levelCFlag) {
        return ''
      } else if (this.isNew) {
        return 'The actual loan amount is subject to the final disbursement amount.'
      }
      return ''
    },
    disbursementAccount() {
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      if (chooseBankAcct) {
        return chooseBankAcct.bankName + (chooseBankAcct.bankAcctNo ? `(${chooseBankAcct.bankAcctNo.slice(-4, chooseBankAcct.bankAcctNo.length)})` : '')
      }
      return ''
    },
    defaultBankImg() {
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      console.log('chooseBankAcct', chooseBankAcct)
      if (!this.isWalletWithdrawalUser) {
        if (chooseBankAcct.bankCode === '090574') {
          return this.goldManImg;
        } else {
          return this.backImg
        }
      } else {
        return this.ninBankImg
      }
    },
    // 是否勾选了goldman
    isChooseGoldmanAccount() {
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      console.log('chooseBankAcct', chooseBankAcct)
      if (this.bankIndex < 0) return false;
      if (!this.isWalletWithdrawalUser) {
        if (chooseBankAcct.bankCode === '090574') {
          return true;
        } else {
          return false
        }
      } else {
        return false
      }
    },
    chooseBankAcct() {
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex] || {};
      if (chooseBankAcct) {
        this.withdrawReport(`withdraw_${chooseBankAcct.bankName}_checkbox_view`);
      }
      return chooseBankAcct;
    },
    otherBankAcct() {
      let otherBankAcct = this.loanDetail.bankAcctList[this.bankIndex] || {};
      if (otherBankAcct.bankName) {
        this.withdrawReport(`withdraw_${otherBankAcct.bankName}_checkbox_view`);
      }
      // 若goldman为默认账号，则显示其他最近绑定的；若勾选了其他的非goldman账户，则显示其他的。
      if (otherBankAcct.bankCode === '090574') {
        console.log('otherBankAcct', this.loanDetail.bankAcctList.find(item => item.latestAccountFlag === 'Y'))
        otherBankAcct = this.loanDetail.bankAcctList.find(item => item.latestAccountFlag === 'Y') || {};
      }
      console.log('otherBankAcct', otherBankAcct)
      return otherBankAcct;
    },
    canShowAddPersonalInformation() { // 能否显示增信bannner
      return this.showIncreaseCredit === 'Y' && this.androidVersion > 28;
    }
  },
  data: function () {
    return {
      phone: '',
      backImg,
      ninBankImg,
      goldManImg,
      pick,
      addBankIcon,
      tips,
      arrowDown,
      unchecked,
      withdrawAlert,
      cancel,
      showRelonTips: false, // 显示还款再借的提示
      showBackDialog: false, // 促销弹窗
      showSubmitDialog: false, // 提交提现时冻结弹框控制
      showBackPopup: true, // 返回弹窗（已废弃）
      show: false,
      show1: false,
      longActive: -1,
      purposeActive: -1,
      couponList: [],
      selectedCoupon: '',
      showMsg: '',
      loanDetail:{
        amount: 0,
        minAmt: 0,
        oldAmount: 0,
        usableLmtAmt: 0,
        multipleUsableLmtAmt: 0, // 多期时，用户额度上限
        productList: [], // 产品列表
        temProductList: [], // 临时产品列表：用于比对使用提额降息券后获取的产品最大可借额度的变化
        purposeList: ['Business', 'House rent', 'Hospital', 'Pharmacy', 'Education', 'Agric', 'Auto', 'Personal', 'Salary advance', 'Other'], // 目的
        purpose: '',
        repayment: {
          minAmt: 0,
          interestNum: 0,
          interestRate: '',
          plan: 0,
          rate: ''
        },
        bankDetail: {
          bankType: '',
          bankName: '',
          bankAcctNo: ''
        },
        bankAcctList: [],
        bankCardList: [],
        repaymentDetailList: []
      },
      placeholderTips: '',
      showDialog: false,
      couponAmt: 0, // 选择的coupon
      showPin: false,
      pinList: [{ // pinList渲染列表
        value: '',
        showInput: true
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }],
      optCount: 0,
      enableReGetOtpBtn: true,
      captcha: '', // 提交字段otp
      showOtpVerifyPop: false, // OTP verify pop
      otpList: [{ // otpList 渲染列表
        value: '',
        showInput: true
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }],
      defaultLoanAccount: {
        bankCode: '',
        bankAcctNo: ''
      },
      chooseTypeData: { // 选择的产品对应的最大值和最小值。
        minAmount: 0,
        maxAmount: 0
      },
      isPin: true, // 是否已设置pin
      showCardList: false, // 展示银行卡列表
      notPurpose: false,
      gpsInfo: {},
      wifiList: '',
      deviceId: '',
      bankIndex: 0,
      availCouponsNum: 0, // 可用优惠券数目
      funArr: [], // 执行的函数数组
      withdrawType: 'withdraw', // 借款类型
      activateState: false, // trade注册状态
      APPConfig: {
        "repaymentLoanPageFlag":"", // 是否使用还款界面
        "h5RepaymentLoanPageUrl":"",
        "applyLoanPageFlag":"", // 是否使用提现界面
        "h5ApplyLoanPageUrl":"",
        "bindBankCardPageFlag":"", // 是否使用个人详情绑卡界面
        "withdrawBindBankCardPageFlag": "", // 是否使用提现绑卡页面
        "h5bindBankCardPageUrl":"",
        "couponPageFlag":"", // 是否使用优惠券界面
        "h5couponPageUrl":"",
        "bindBankAccountPageFlag":"", // 是否使用绑账户界面
        "bindBankAccountPageUrl":"",
        "isSkipPINFlag": "" //Y 跳过pin N 使用pin
      },
      addBankFromWithdraw: false, // 是否从提现弹出的绑账户
      cardSkip: false, // true 绑卡可跳过  false 绑卡不可跳过
      haveCard: false, // true 已绑卡 false 没有卡
      hasLoan: false, // 当前用户是否有借款（用于判断AF上报）
      afFlag: '', // af上报标志
      proIndex: -1, // 默认勾选状态。
      showMinAlert: false,
      showProductList: false, // 展示产品列表
      startBuriedPoint: [], // 延迟数据上报数组
      isWitdrawShow: false, // 目的展示是否由提现触发
      caculateStatus: false, // 试算状态
      userInfo: {
        custId: '',
        xcrossCustId: '',
        birthday: '',
      },
      currentAppVersionName: '',
      appVersion: '',
      applyInfo: {
        applyNo: '',
        batchNo: ''
      },
      minAlertTips: '',
      showAtLeastCheck: false,
      showTips: false,
      freezeMsg: '', // 冻结的提示
      haveLoanFreezeMsg: 'You still have some outstanding orders on our platform. Please try again after you pay them off.', // 有在途借据的提示
      checkShow: '', // 是否默认勾选保险费
      showReasonPopup: false, // 二挡起提原因选择。选择Minimum amount more than need会触发二挡起提逻辑。
      isSecondAmount: false, // 是否开启二挡额度
      reloan: false, // 是否是循环贷标识
      isNew: false, //针对本产品新客展示固定文案
      levelCFlag: false, // 风控C类用户标签
      isAllProductNew: false, // 全产品新客
      showNoProDialog: false,
      popTips: '',
      showGiftPopup: false, // 二挡起提弹窗
      stepGiftPopup: '1',
      identityType: '', // 用户类型（NIN、bvn）
      activeUid: '', // 三项数据上报使用的uid
      maxUsableLmtAmt: 0,
      queryStageInfoPurpose: '',
      tag14and2User: false, // 是否是默认14*2的用户以及有28天单期产品的用户
      lastPaidOffLoan: {}, // 用户上一笔借据
      transactionPassword: '',
      activityStatus: '', // 活动状态
      remindUpLimit: 0, // 用户提额
      withdrawProcessNum: '', // 提现流程类型
      reasonType: 'back', // back 返回的弹窗 question 点击question浮窗的弹窗
      reasonObj: {
        back: {
          title: 'What is your reason for forgoing the loan?',
          reasonType: 'back',
          list: [{
            key: 1,
            name: 'The loan amount is not enough',
            type: 'banner',
            banner: banner1
          }, {
            key: 2,
            name: 'The interest rate is too high',
            type: 'banner',
            banner: banner2
          }, {
            key: 3,
            name: 'Withdrawal has a minimum amount limit',
            type: 'banner',
            banner: banner3
          }, {
            key: 4,
            name: 'The term of the installment loan is lower than expected',
            type: 'text'
          }, {
            key: 5,
            name: 'Do not want the loan to be disbursed to a Goldman Bank account',
            type: 'text'
          }, {
            key: 6,
            name: 'There is no loan demand at present',
            type: 'text'
          }, {
            key: 7,
            name: 'Others',
            type: 'text'
          }]
        },
        question: {
          title: "What's your question?",
          reasonType: 'question',
          list: [{
            key: 1,
            name: 'The loan amount is not enough',
            type: 'text'
          }, {
            key: 2,
            name: 'The interest rate is too high',
            type: 'text'
          }, {
            key: 3,
            name: "Withdrawal has a minimum amount limit",
            type: 'text'
          }, {
            key: 4,
            name: 'The term of the installment loan is lower than expected',
            type: 'text'
          }, {
            key: 5,
            name: "I don't need so much money",
            type: 'text'
          }, {
            key: 6,
            name: 'Others',
            type: 'textarea',
            textAreaContent: '',
            placeholder: 'Describe the details'
          }]
        }
      },
      goldmanBankAcctType: '', // 账户类型 1-一类户  2-二类户
      enableGoldman: '', // 当前用户是否处于goldman灰度
      ninCheckCb: '', // nin检查后的回调
      isSupportBalance: false, // 是否支持balance
      faqGoldmanSetting: {}, // 显示配置
      goldmanDebitScene: '', //goldman代扣场景  1:用户未开通该goldman账户 ,2:用户已开通goldman账户，但未签署代扣协议 3.用户已开通goldman账户且已签署代扣协议
      goldManAccount: '', // 用户的goldMan账户信息
      singleProductList: [], // 单期产品存储
      muiltiProductList: [], // 多期产品存储
      showAddPersonalInformation: false, // 是否显示补件弹窗
      showIncreaseCredit: '', // 是否显示补件banner
      androidVersion: 0, // 当前安卓版本
      faceScene: '', // 人脸验证场景值
      showGiftDialog: false, // 礼盒挽留弹窗
      giftDialogType: '', // 礼盒挽留弹窗类型
      giftDialogActivityText: '', // 提示语
      disableBackPopup: false, // 是否禁用挽留弹窗
      fromName: '', // 页面来源
      isWalletWithdrawalUser: false, // 是否是钱包提现用户
    }
  },
  methods: {
    closePinPop() {
      this.showPin=false
      this.retPin()
    },
    closeOtpVerifyPop(type) {
      clearInterval(this.optTimer);
      this.optTimer = null;
      this.enableReGetOtpBtn = true
      this.showOtpVerifyPop = false
      this.optCount = 0
      // 非来源于设备检查的时候，清空验证码
      if (type !== 1) {
        this.captcha = '';
      }
      this.resetOtp()
    },
    chooseOtpVerify() {
      const vm = this
      this.showOtpVerifyPop = true
      this.captcha = ''
      this.resetOtp()
      this.enableReGetOtpBtn = true
      // 触发otp查询，发送短信验证码
      this.queryOtp('hand')
      // 开始120s倒计时
      this.waiting120Seconds()
      vm.$nextTick(() => {
        if (!window.webkit) {
          vm.$refs['inputOtp0'][0].focus()
        }
      });
    },
    waiting120Seconds() {
      const opt_TIME_COUNT = 120
      this.optCount = opt_TIME_COUNT
      this.enableReGetOtpBtn = false
      this.optTimer = setInterval(() => {
        if (this.optCount > 0 && this.optCount <= opt_TIME_COUNT) {
          this.optCount--;
        } else {
          clearInterval(this.optTimer);
          this.optTimer = null;
          // 倒计时结束
          this.enableReGetOtpBtn = true
        }
      }, 1000);
    },
    reGetOtpBtnClick() {
      if(this.enableReGetOtpBtn) {
        this.waiting120Seconds()
        this.queryOtp('hand')
      }
    },
    queryOtp(flag) {
      let scene = ''
      if(flag === 'auto') {
        scene = 'AUTO_SEND'
      } else {
        scene = 'ACT_SEND'
      }
      // 提交接口，重新获取otp
      this.$http(api.commonBlc.applyLoanCaptchaSend, {
        data: {
          scene: scene // 自动发送短信：AUTO_SEND,主动发送短信：ACT_SEND
        }
      }).then(res => {
        console.log('applyLoanCaptchaSend--res', res)
        if(flag === 'auto' && !this.showOtpVerifyPop) {
          this.showOtpVerifyPop = true
          this.waiting120Seconds()
          this.captcha = ''
          this.resetOtp()
          this.$nextTick(() => {
            if (!window.webkit) {
              this.$refs['inputOtp0'][0].focus()
            }
          });
        }
      })
      .catch(err => {
        console.log('err', err)
      })
    },

    accountSlice(account) {
      return account ? `(${account.slice(-4, account.length)})` : '';
    },
    // 针对单产品新客展示固定文案
    getUserType() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.getUserType, {
          data: {
            // custId: custId // 接口不能再传custId， 后台要自己获取，因为有无bvn的用户了
          }
        }).then(res => {
          console.log('getUserType===res', res)
          vm.isNew = res.isNew;
          vm.levelCFlag = res.levelCFlag;
          vm.isAllProductNew = res.isAllProductNew
          vm.phone = res.phone
          resolve(res);
        }).catch(e => {
          reject(e);
        })
      })
    },
    // 获取当前用户设备验证的类型
    getDeviceVerification() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.deviceVerification, {
          data: {
            deviceId: this.deviceId,
            supportFaceDetectionFlag: this.supportedFeatures.faceVerify === 'true' ? 'Y' : 'N' // 当前设备是否支持人脸识别
          }
        }).then(res => {
          console.log('deviceVerification===res', res)
          this.$store.commit('SET_CHECKDEVICESTAGE', res.checkDeviceStage);
          this.faceScene = res.faceScene;
          resolve(res);
        }).catch(e => {
          reject(e);
        })
      })
    },
    // 获取用户授权等信息
    queryStageInfo() {
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.queryStageInfo4nc, {
        }).then(res => {
          console.log('queryStageInfo4nc===res', res)
          this.identityType = res.identityType;
          // this.identityType = 'NIN'; // 仅供调试
          // 每次更新，不写死，以防后续逻辑变更
          if (this.identityType === 'NIN') {
            localStorage.setItem('customerType', 'NIN');
          } else {
            localStorage.setItem('customerType', '');
          }
          // 无bvn用户，三项数据上报补偿。
          if (this.activeUid) {
            // res.idOriginal为明文的bvn 或者 nin.
            const custId = res.idOriginal;
            localStorage.setItem('customerId', custId);
            activateTradeBigdata(this.activeUid, custId, 'activateTradeBigdataCb');
          }
          // 有借款目的，默认给用户设置
          this.queryStageInfoPurpose = res.loanPurpose;
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      })
    },
    // 获取用户预提现订单信息
    queryNoBindCardInfo() {
      return  new Promise((resolve) => {
        this.$http(api.withdraw.queryNoBindCardInfo, {
        }).then(res => {
          resolve(res);
        }).catch(e => {
          // 报错不能堵塞流程
          resolve(e);
        });
      })
    },
    acWithdrawPageReport(eventName, error, eventData) {
      let vm = this;
      vm.$store.dispatch('acReportEvent', {
        page: 'withdraw',
        eventName: eventName,
        param: {
          arg1: error
        },
        eventData: eventData
      });
    },
    // 显示产品选择
    selectTerm() {
      this.showProductList = true;
    },
    closeProduct() {
      this.showProductList = false;
    },
    hideReasonPopup(obj) {
      this.showReasonPopup = false;
      if (this.reasonType === 'back') {// 返回的调研弹窗
        if (obj && obj.type) {
          // 用户提交原因后7天内不再弹调研弹窗：
          localStorage.setItem('reasonSubmitCloseLimit', new Date().getTime());
          let closeType = '';
          if (obj.reasonActive <= 2) {
            closeType = `banner${this.reasonObj[this.reasonType].list[obj.reasonActive].key}`
          }
          if (obj.reasonActive !== 2) {
            localStorage.setItem('otherAtLeastFlag', new Date().getTime());
          }
          this.withdrawReport('withdraw_popup_reason_submit_click');
          if (obj.type === 'close') {
            gotoHomeActivity('index');
            this.withdrawReport('cancel_click');
          } else if (obj.type === 'submit') {
            if (closeType === 'banner1' || closeType === 'banner2') {
              let triggerScene = '';
              if (closeType === 'banner1') {
                triggerScene = 'WITHDRAWAL_UPLIMIT';
              } else if (closeType === 'banner2') {
                triggerScene = 'WITHDRAWAL_RETENTION';
              }
              // 命中客群，弹出增信弹框
              if (this.canShowAddPersonalInformation && closeType === 'banner1') {
                this.showAddPersonalInformation = true;
                this.withdrawReport('withdraw_addinfo_banner1_click');
              } else {
                // 发送优惠券，并刷新优惠券，重新试算
                this.queryCreateCoupon(triggerScene).then(() => {
                  if (triggerScene === 'WITHDRAWAL_UPLIMIT') {
                    this.$newToast('The limit up coupon has been issued. You will enjoy up to 200,000 limit on your next loan.');
                  } else {
                    this.$newToast('An interest coupon has been sent to you')
                  }
                  this.queryCouponList4nc({
                    "useScene":"1"
                  }).then(() => {
                    this.startLoanCalculate();
                  })
                })
              }
            } else if (closeType === 'banner3') {
              if (!this.showAtLeastCheck) {
                this.showAtLeastCheck = true;
              }
              // 若未触发二挡起提，则触发。
              if (!this.isSecondAmount || this.inTimeAreaCal('reduceWithdrawalLimit', 48) && this.isSecondAmount) {
                this.$newToast('Submit successfully');
              } else {// 若未触发二挡起提，则触发。
                localStorage.setItem('reduceWithdrawalLimit', new Date().getTime());
                this.startSecondAmt();
              }
              this.withdrawReport('submit_click');
            } else if ([3, 4, 5, 6].includes(obj.reasonActive)) { // 这几种情况都跳转surveyActivity页面
              this.$router.push({ path: 'surveyActivity'});
            }
          }
        } else {
          this.withdrawReport('withdraw_popup_reason_off_click');
          this.$router.push({ path: 'surveyActivity'});
          // 每天最多只弹出1次（即用户不选择原因，仅关闭弹窗，则当天不再弹出）
          localStorage.setItem('onlyCloseLimit', new Date().getTime());
          //gotoHomeActivity('index');
        }
      } else if (this.reasonType === 'question') {
        if (obj.type === 'close') {
          gotoHomeActivity('index');
        } else if (obj.type === 'submit') {
          this.withdrawReport('withdraw_question_pop_submit_click');
          if (obj.reasonActive === 2 || obj.reasonActive === 4) {
            // 若判断当前二档起提开关未打开，或用户已触发二档起提限制，则提交时toast提示：Submit successfully
            if (!this.isSecondAmount || this.inTimeAreaCal('reduceWithdrawalLimit', 48) && this.isSecondAmount) {
              this.$newToast('Submit successfully');
            } else {// 若未触发二挡起提，则触发。
              localStorage.setItem('reduceWithdrawalLimit', new Date().getTime());
              this.startSecondAmt();
            }
          } else {
            // 命中客群，弹出增信弹框
            if (this.reasonObj[this.reasonType].list[obj.reasonActive].key === 1 && this.canShowAddPersonalInformation) {
              this.showAddPersonalInformation = true;
              this.withdrawReport('withdraw_addinfo_question_click');
            } else {
              this.$newToast('Submit successfully');
            }
          }
        }
      }
    },
    startSecondAmt() {
      if (this.inTimeAreaCal('reduceWithdrawalLimit', 48) && this.isSecondAmount) {
        const product = this.loanDetail.productList[this.longActive];
        const productFee = product.productFee[0];
        this.loanDetail.amount = this.thousands(productFee.secondMinAmt);
        this.$newToast('We have reduced the withdrawal limit for you，try to withdrawal again!');
        this.startLoanCalculate();
      }
    },
    hideProDialog() {
      this.hideProDialog = false;
      gotoHomeActivity('index');
    },
    changeBankCard(index) {
      let vm = this;
      vm.bankIndex = index;
      vm.showCardList = false;
    },
    // AF上报
    startAppsflyerData() {
      // 调用广告回传: 每个用户只调用一次
      // 已上报，来自AC，已有借款，不再重新上报。
      let vm = this;
      vm.afFlag = localStorage.getItem('applyAppsflyerData');
      if (!vm.afFlag && vm.deviceType !== 'AC' && !vm.hasLoan) {
        applyAppsflyerData();
        localStorage.setItem('applyAppsflyerData', 1);
        // 处理IOS新客上报问题。
        if (judgeClient() === 'IOS') {
          uploadAdsEvent('apply_withdraw')
        }
      }
    },
    getAmount() {
      return +(this.loanDetail.amount ? this.loanDetail.amount.replace(/,/g, '') : 0);
    },
    hideShowTips() {
      console.log('hideShowTips');
      this.showTips = false;
      gotoHomeActivity('index');
    },
    goWithdraw(num) {
      let vm = this;
      vm.addBankFromWithdraw = true;
      vm.withdrawProcessNum = num;
      // 需要当前设备支持人脸才可以走一下功能
      console.log('vm.checkDeviceStage', vm.checkDeviceStage, 'vm.checkDeviceStageResult', vm.checkDeviceStageResult)
      if (vm.supportedFeatures.faceVerify) { // 人脸设备检查
        if (vm.checkDeviceStage === 'UF' && vm.checkDeviceStageResult !== 'Y') { // 需要人脸验证
          // 处于这种情况，是需要先做人脸检测的。所以直接把标识设为Y。
          vm.$store.commit('SET_NEEDRECREDITLIVEDETECTION', 'Y');  // 更新人脸是否提交信息
          vm.$router.push({
            path: '/faceVerification',
            query: {
              type: 'checkDeviceStage'
            }
          });
          return;
        }
      }
      // 走OTP设备检查时没有输入OTP, 弹出输入框输入
      if (vm.checkDeviceStage === 'OTP' && vm.checkDeviceStageResult !== 'Y') {
        vm.chooseOtpVerify();
        vm.APPConfig.isSkipPINFlag = 'Y';
        return;
      }
      // 提现完成人脸验证或OTP验证后，可跳过pin码环节, 直接设置当前用户可跳过pin
      if (this.checkDeviceStageResult === 'Y') {
        vm.APPConfig.isSkipPINFlag = 'Y';
      }
      if (vm.supportedFeatures.faceVerify) {
        // 未放款用户且授信通过距今超过180天的（未冻结状态）
        // 进入提现页，点击提现按钮，需先补填紧急联系人（点选）信息和完成人脸验证，才可提交提现（新数据覆盖老数据）
        if (vm.needReCreditContractList === 'Y') { //
          vm.$router.push({
            path: '/updateEmergencyContact'
          });
          return;
        } else if (vm.needReCreditliveDetection === 'Y') {
          vm.$router.push({
            path: '/faceVerification',
            query: {
              needReCreditliveDetection: vm.needReCreditliveDetection
            }
          });
          return;
        }
      }
      vm.retPin();
      vm.resetOtp()
      vm.withdrawReport('withdraw_button_click');
      // 添加区分的埋点。
      vm.withdrawReport(`withdraw_button_click_${num}`);
      log('withdrawPage', `withdraw_button_click_${num}`);
      if (vm.deviceType === 'AC') {
        vm.acWithdrawPageReport('withdraw_submit');
      }
      const amount = vm.getAmount();
      if (amount <= 0) {
        vm.withdrawReport(`withdraw_process_amount_error_${num}`);
        vm.$toast({
          message: `<div style="width: 240px">Please enter withdraw amount</div>`,
          type: 'html'
        });
        return;
      }
      if (vm.longActive === -1) {
        vm.withdrawReport(`withdraw_process_not_choose_tenor_error_${num}`);
        vm.$toast({
          message: 'Please select loan tenor'
        });
        return;
      }
      if (vm.purposeActive === -1) {
        vm.withdrawReport(`withdraw_process_not_choose_purpose_error_${num}`);
        vm.show1 = true;
        vm.isWitdrawShow = true;
        return;
      }
      if (amount < vm.chooseTypeData.minAmount || amount > vm.chooseTypeData.maxAmount) {
        vm.withdrawReport(`go_withdraw_amount_${amount}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}`);
        vm.$toast({
          message: `<div style="width: 240px">Withdraw amount is ₦${vm.chooseTypeData.minAmount}-₦${vm.chooseTypeData.maxAmount}</div>`,
          type: 'html'
        });
        return;
      }
      if (vm.loanDetail.bankAcctList.length === 0) {
        vm.withdrawReport(`withdraw_process_not_bank_acct_error_${num}`);
        // 无银行账户情况下跳转添加银行账户页面
        vm.showDialog = true;
        return;
      }
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      if ((vm.loanDetail.bankCardList.length === 0 && (!vm.cardSkip) && !vm.haveCard) && vm.deviceType !== 'AC' && !this.isWalletWithdrawalUser && vm.isNew) {
        // 非绑卡跳过、非NIN用户, 新客
        // 同时触发预提现逻辑
        vm.loanApplyNoBindCardFlag = true;
        vm.withdrawReport(`withdraw_process_loan_apply_no_bind_card_${num}`);
        vm.loanApplyNoBindCardInit().then(() => {
          // 跳转添加银行卡
          vm.ninCheckCb = () => {
            vm.addBank(1);
          }
          console.log('hideLoading');
          vm.$hideLoading();
          vm.judgeNinCheck();
        }).catch((e) => {
          // 非设备检测OTP可以正常走
          if (!(e.code === 1208005 && this.checkDeviceStage === 'OTP')) {
            // 跳转添加银行卡
            vm.ninCheckCb = () => {
              vm.addBank(1);
            }
            console.log('hideLoading');
            vm.$hideLoading();
            vm.judgeNinCheck();
          }
        })
        return;
      }
      console.log('vm.isPin', vm.isPin)
      console.log('vm.APPConfig.isSkipPINFlag', vm.APPConfig.isSkipPINFlag)
      console.log('vm.deviceType', vm.deviceType)
      // 如果没有设置pin,则需要设置pin
      if (vm.isPin || vm.APPConfig.isSkipPINFlag === 'Y' || vm.deviceType === 'AC') {
        // 判断用户的信息是否被冻结
        if (vm.APPConfig.isSkipPINFlag !== 'Y' && vm.deviceType !== 'AC') {
          vm.withdrawReport(`withdraw_process_show_pin_${num}`);
          vm.showPin = true;
          vm.$nextTick(() => {
            if (!window.webkit) {
              vm.$refs['input0'][0].focus()
            }
          });
        } else {
          vm.withdrawReport(`withdraw_process_start_loan_apply_${num}`);
          // 未开户的goldman走预提现
          if (chooseBankAcct.bankCode === '090574' && !chooseBankAcct.bankAcctNo) {
            this.goNotGoldmanSubmit();
          } else {
            vm.ninCheckCb = () => {
              vm.startLoanApply();
            }
            vm.judgeNinCheck();
          }
        }
      } else { //设置pin
        vm.withdrawReport(`withdraw_process_set_pin_${num}`);
        vm.$router.push({ name: 'setPin' });
      }
    },
    // 未开户的goldman走预提现
    goNotGoldmanSubmit(password) {
      this.loanApplyNoBindCardInit(password).then(() => {
        this.$hideLoading();
        this.ninCheckCb = () => {
          // 老客提交后，直接跳转到提现页
          const loanApplyNoBindCardInitData = localStorage.getItem('loanApplyNoBindCardInitData');
          this.$router.push({
            path: "/withdrawStatus",
            query: {
              data: loanApplyNoBindCardInitData
            }
          });
        }
        this.judgeNinCheck();
      }).catch((e) => {
        this.$hideLoading();
        if (e.status && e.status.msg) {
          this.$newToast(e.status.msg)
        } else {
          this.$newToast('Submit Error, please contact customer service');
        }
      })
    },
    judgeNinCheck() {
      // // nincheck判断来源
      // const amount = this.getAmount();
      // const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      // // 提交提现时，判断放款账户是否选择的是goldman bank账户且是一类户（即Level 1，未开户默认为一类户, 开户后也是默认一类户，只有nin验证后，才能升级为二类户）
      // // 且提现金额大于50000，是则弹窗提示用户验证NIN
      // if (amount > 50000 && chooseBankAcct.bankCode === '090574' && this.goldmanBankAcctType === '1') {
      //   console.log('this.$refs.ninCheck', this.$refs.ninCheck);
      //   this.withdrawReport('nin_verify_popup_view');
      //   this.$refs.ninCheck.showCheck();
      // } else {
      //   this.ninCheckCb();
      // }
      // 暂时屏蔽nin弹窗
      this.ninCheckCb();
    },
    // 选择分期产品
    chooseLoanTermType(param) {
      this.chooseType(param.index, param.type);
    },
    /**
     * 选择产品并触发试算
     * @param {Number} index 选择产品索引值
     * @param {*} type 触发类型
     * @param {*} showToast 是否弹窗
     * **/
    chooseType(index, type, showToast) {
      let vm = this;
      if (index < 0) return;
      vm.caculateStatus = false;
      let product = vm.loanDetail.productList[index];
      let productFee = product.productFee[0];
      let amount = vm.getAmount();
      vm.longActive = index;
      let maxAmount = 0;
      let minAmount = 0;
      if (type === 'click') { // 用户的点击
        vm.withdrawReport(`${product.borrowCount}D${productFee.term}T_click`);
      }
      if (productFee.secondMinAmt && productFee.secondMaxAmt) { // 存在用户主动调整额度时，使用调整二挡调整额度。
        vm.isSecondAmount = true;
        if (vm.inTimeAreaCal('reduceWithdrawalLimit', 48)) {
          productFee.minAmt = productFee.secondMinAmt;
          productFee.maxAmt = productFee.secondMaxAmt;
        }
      } else { // 否则，清除本地缓存的标识。
        localStorage.removeItem('reduceWithdrawalLimit');
        // localStorage.removeItem('otherAtLeastFlag');
      }
      vm.loanDetail.minAmt = productFee.minAmt ? productFee.minAmt : vm.loanDetail.minAmt;
      maxAmount = productFee.maxAmt;
      minAmount = productFee.minAmt;
      // 用户进提现页时，判断若用户剩余可借额度为0： 直接使用产品maxAmount判断。
      if (+maxAmount <= 0) {
        vm.popTips = 'You have reached the loan limit in other APPs. Please pay off the loans in other APPs first.';
        vm.showNoProDialog = true;
      }
      if (!showToast) { // 切换产品时，使用产品的最大值
        vm.loanDetail.amount = vm.thousands(maxAmount);
      } else {
        if ((maxAmount < amount || minAmount > amount)) { // 正常输入时，只有不在产品区间才设置为最大值。
          vm.loanDetail.amount = vm.thousands(maxAmount);
        }
      }
      vm.chooseTypeData.minAmount = minAmount;
      vm.chooseTypeData.maxAmount = maxAmount;
      vm.minAlertTips = `At least ₦${vm.common.singleToThousands(vm.chooseTypeData.minAmount)}`;
      if (vm.showMinAlert) {
        // Todo: 这里的流程，可以提前到productFee.secondMinAmt && productFee.secondMaxAmt中判断。合并vm.showMinAlert的判断即可。下次版本调整下。
        // 这里的逻辑跟上面重复了，没有必要这样处理。
        if (vm.productSource === 'xcrosscash') { // 先只针对xcrosscash开放
          // 判断用户当前是否已触发开启二档起提限制，有则正常提示报错
          if (vm.inTimeAreaCal('reduceWithdrawalLimit', 48) && vm.isSecondAmount) {
            vm.$newToast(vm.minAlertTips);
            vm.withdrawReport(`input_amount_${amount}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}`);
          } else if (vm.isSecondAmount) {
            // 若未开启二档起提，则触发开启二档起提
            localStorage.setItem('reduceWithdrawalLimit', new Date().getTime());
            productFee.minAmt = productFee.secondMinAmt;
            productFee.maxAmt = productFee.secondMaxAmt;
            maxAmount = productFee.maxAmt;
            minAmount = productFee.minAmt;
            vm.chooseTypeData.minAmount = minAmount;
            vm.chooseTypeData.maxAmount = maxAmount;
            // 然后判断该金额是否低于二档起提金额，是则仍正常提示At least XXX，否则可正常提现，不需要提示
            if (minAmount > amount) {
              vm.minAlertTips = `At least ₦${vm.common.singleToThousands(minAmount)}`;
              vm.loanDetail.amount = vm.thousands(minAmount);
              vm.$newToast(vm.minAlertTips);
              vm.withdrawReport(`input_amount_${amount}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}`);
            } else {
              vm.showMinAlert = false;
            }
          }
        } else {
          vm.$newToast(vm.minAlertTips);
          vm.withdrawReport(`input_amount_${amount}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}`);
        }
      }
      vm.placeholderTips = `At least ₦${vm.thousands(minAmount)}, maximum ₦${vm.thousands(maxAmount)}`;
      // 重新计算优惠券可使用情况
      vm.availCouponsNum = 0;
      let couponList = [];
      vm.couponList.forEach(item => {
        if (vm.calculateCouponCanUse(item, product.borrowCount, vm.getAmount())) {
          couponList.push(item);
          vm.availCouponsNum++;
        }
      });
      let saveCoupon = false;
      couponList.map(item => {
        if (vm.coupon && item.couponId === vm.coupon.couponId) { // 说明优惠券仍然可用。
          saveCoupon = true;
        }
      });
      if (!saveCoupon) {
        vm.$store.commit('SET_COUPON', {});
      }
      if (((product.isPcx && productFee.insuranceRate > 0) || (productFee.insuranceRate && productFee.insuranceRate.feeRate > 0)) && vm.checkShow === '') {
        vm.checkShow = true;
      }
      let enableInsurance = (product.isPcx && productFee.insuranceRate > 0) || (productFee.insuranceRate && productFee.insuranceRate.feeRate > 0) ? vm.checkShow : '';
      if (index > -1 && !product.isPcx) {
        vm.loanApplyCalc({
          loanAmt: vm.getAmount(),
          loanSpan: product.borrowCount,
          loanTerm: productFee.term,
          loanType: product.loanType,
          needEncryptContract: true, // 需要获取base64的合同
          productFeeId: productFee.productFeeId,
          txnScene: product.txnScene,
          enableInsurance: enableInsurance,
          couponId: vm.coupon? vm.coupon.couponId : '',
        }).then(res => {
          console.log('提现试算接口--loanCalculate4nc===res', res)
          let insuranceFee = 0;
          if (enableInsurance) {
            res.loanCalcVOList.forEach(item => {
              if (item.key === 'Credit Protection Service') { // 若字段改变，后端也要变更这个字段
                insuranceFee = item.value;
              }
            })
          }
          res.plans.forEach(item => {
            item.dueDate = dateFormat(new Date(item.dueDate.replace(/-/g, '/')), 'MM/dd/yyyy');
          })
          vm.loanDetail.repayment = {
            ...vm.loanDetail.repayment,
            actualTotalAmount: res.actualTotalAmount,
            showInsurance: productFee.insuranceRate && productFee.insuranceRate.feeRate > 0,
            insuranceFee: insuranceFee,
            rate: `${(productFee.rate * 100).toFixed(2)}%`,
            platformFee: (Number( productFee.pltfRate ? productFee.pltfRate * res.actualTotalAmount : 0)).toFixed(2),
            ...res
          };
          vm.$store.commit('SET_CONTRACT', vm.$decode(res.contractHtml));
          vm.loanDetail.repaymentDetailList = res.plans;
          vm.loanDetail.actualTotalAmount = res.actualTotalAmount;
          console.log(vm.loanDetail.repayment);
          vm.caculateStatus = true;
        }).catch(e => {
          console.log(e);
        })
      }
      vm.closeProduct();
    },
    thousands(num) {
      return this.common.singleToThousands(num);
    },
    startPurpose() {
      this.choosePurpose();
      if (this.stepGiftPopup === '1') {
        this.withdrawReport('withdraw_popup_one_select_click');
      } else {
        this.withdrawReport('withdraw_popup_two_select_click');
      }
    },
    choosePurpose() {
      this.show1 = !this.show1;
      this.withdrawReport('withdrawpage_loanpurpose_button_click');
    },
    selectedPurpose(index) {
      const vm = this;
      vm.loanDetail.purpose = vm.loanDetail.purposeList[index];
      vm.purposeActive = index;
      if (vm.isWitdrawShow) { // 是否由提现弹出
        vm.isWitdrawShow = false;
        vm.show1 = false;
        // 要先关闭再跳转
        vm.$nextTick(() => {
          vm.goWithdraw(1);
        })
      } else {
        vm.show1 = false;
      }
    },
    closePurpose() {
      this.isWitdrawShow = false;
    },
    showPlan() {
      const vm = this;
      if (vm.caculateStatus) {
        vm.show = true;
      }
    },
    closePlan() {
      this.show = false;
    },
    // 输入控制
    keyDown(e) {
      let keyCode = e.keyCode;
      if ((keyCode < 48 || keyCode > 57) && keyCode != 8) {
        e.preventDefault();
      }
    },
    // 控制显示
    keyUp() {
      let vm = this;
      let value = vm.loanDetail.amount.replace(/\D+/g, '');
      if (value < vm.chooseTypeData.minAmount) {
        vm.showMinAlert = true;
        vm.minAlertTips = `At least ₦${vm.common.singleToThousands(vm.chooseTypeData.minAmount)}`;
        if (value) {
          vm.loanDetail.amount = vm.thousands(value);
        }
        return;
      } else if (value > vm.chooseTypeData.maxAmount) {
        vm.showMinAlert = false;
        vm.$toast({
          message: `<div style="width: 200px">At most ₦${vm.common.singleToThousands(vm.chooseTypeData.maxAmount)}</div>`,
          type: 'html'
        });
        vm.loanDetail.amount = vm.loanDetail.oldAmount;
        vm.withdrawReport(`input_amount_${value}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}`);
        return
      }
      if (value) {
        vm.showMinAlert = false;
        vm.loanDetail.amount = vm.thousands(value);
      }
    },
    // 选择优惠券
    chooseCoupons() {
      let vm = this;
      let product = vm.loanDetail.productList[vm.longActive] ? vm.loanDetail.productList[vm.longActive] : {};
      let productFee = product.productFee ? product.productFee[0] : {};
      vm.withdrawReport('withdrawpage_coupon_button_click');
      vm.$router.push({ path: "/couponsList", query: {
        productFeeId: productFee.productFeeId ? productFee.productFeeId : '',
        productId: product.productId ? product.productId : '' ,
        mLoanSpan: product.borrowCount ? product.borrowCount : '',
        mLoanAmt: vm.getAmount(),
        term: productFee.term ? productFee.term : 0,
        availCouponsNum: vm.availCouponsNum,
        from: 'withdraw'
      }});
    },
    // bulr后再执行一次计算。
    startBlurLoanCalculate() {
      let vm = this;
      vm.chooseType(vm.longActive, 'blur', 1);
    },
    startLoanCalculate() {
      let vm = this;
      vm.chooseType(vm.longActive, 0, 1);
    },
    // 跳转合同
    viewContract() {
      let vm = this;
      vm.$router.push({ name: "contract" });
    },
    // 缓存借款详情
    saveWithdrawData() {
      const vm = this;
      let widthdrawData = {
        amount: vm.loanDetail.amount,
        longActive: vm.longActive,
        purposeActive: vm.purposeActive,
        bankIndex: vm.bankIndex,
        coupon: vm.coupon,
        checkShow: vm.checkShow
      };
      localStorage.setItem('widthdrawData', JSON.stringify(widthdrawData));
    },
    // 增加银行卡账号
    addBank(type) {
      let vm = this;
      if (type === 1) {
        // 添加银行卡（还款）
        vm.withdrawReport('withdrawpage_addbankcard_button_click');
        if (vm.APPConfig.withdrawBindBankCardPageFlag === 'Y') {
          // 第一次添加H5银行卡的时候，缓存提现的数据
          vm.saveWithdrawData();
          vm.addBankRequest();
        } else {
          vm.nativeBindBankCard();
        }
      } else if (type === 2) {
        vm.withdrawReport('withdrawpage_addbankaccount_button_click');
        // 绑定银行账户（提现）
        if (vm.APPConfig.bindBankAccountPageFlag === 'Y') {
          vm.$router.push({ path: '/addBankAccount'});
        } else {
          vm.nativeBindBankAccount();
        }
      }
    },
    // 跳转绑卡中台的逻辑
    addBankRequest() {
      let vm = this;
      let obj = {};
      if (vm.productSource === 'palmcredit') {
        obj.businessChannel = 'palmcreditnew';
      }
      // 缓存绑卡标识。用于绑卡结果页判断跳转
      localStorage.setItem('bindCardType', 'widthdrawAddBankCard');
      vm.$loading();
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.routeRedirect, {
          "data":{
            "scene":"2",
            ...obj
          } // 目前是写死的
        }).then(res => {
          // 跳转绑卡中台的地址
          vm.$hideLoading();
          location.href = res.redirectUrl;
          // 缓存银行账户地址。
          vm.withdrawReport('addbankcard_route_redirect_success');
          localStorage.setItem('addBankRequest', res.redirectUrl);
          resolve();
        }).catch(e => {
          vm.withdrawReport('addbankcard_route_redirect_failed');
          reject(e);
        })
      })
    },
    // 请求银行卡
    queryCustomerBankCardList4nc() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.withdraw.queryCustomerBankCardList4nc, {}).then(res => {
          vm.loanDetail.bankCardList = res.bankCardList;
          resolve();
        }).catch(e => {
          reject(e);
        });
      });
    },
    queryWithdrawAcctInfoCutoverSelect4nc() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.withdraw.queryWithdrawAcctInfoCutoverSelect4nc, {
          data:{
            "multipleAccounts":"true"
          }
        }).then(res => {
          if (res.bankAcctList.length > 0) {
            let isWalletWithdrawalUser = false;
            res.bankAcctList.forEach(item => {
              if (item.walletAccountNumber) {
                item.bankAcctNo = item.walletAccountNumber;
                item.bankName = item.verifyType;
                isWalletWithdrawalUser = true;
              }
            })
            this.isWalletWithdrawalUser = isWalletWithdrawalUser;
            if (this.isWalletWithdrawalUser) { // 缓存用户账户类型
              localStorage.setItem('userAccountType', 'isWalletWithdrawalUser');
            } else {
              localStorage.setItem('userAccountType', '');
            }
          }
          vm.loanDetail.bankAcctList = res.bankAcctList;
          resolve();
        }).catch(e => {
          reject(e);
        });
      });
    },
    // 后台计算分期产品还款计划
    loanApplyCalc(data) {
      let vm = this;
      console.log('loanApplyCalc', data);
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.loanCalculate4nc, {
          data:{
            ...data
          }
        }).then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    // 提交借款
    loanApply4nc(data) {
      let vm = this;
      vm.$loading();
      let product = vm.loanDetail.productList[vm.longActive];
      vm.withdrawReport(`loan_apply_submit_start_${vm.withdrawProcessNum}`);
      return  new Promise((resolve, reject) => {
        vm.$http(api.withdraw.loanApply4merge, {
          addHeader: vm.deviceType === 'AC' ? vm.addHeader : {},
          showErrorToast: false,
          data: data
        }).then(res => {
          vm.$hideLoading();
          vm.withdrawReport(`loan_apply_submit_success_${vm.withdrawProcessNum}`);
          this.showPin = false
          this.retPin()
          this.showOtpVerifyPop = false
          this.captcha = ''
          this.resetOtp()
          if (res.mutuallyExclusive === -1) {
            vm.startAppsflyerData();
            // 用户非冻结
            vm.withdrawReport(`current_user_not_frozen_success`);
            if (vm.deviceType === 'AC') {
              vm.acWithdrawPageReport('withdraw_submit_valid');
              vm.acWithdrawPageReport('withdraw_submit_done', '', {
                startup_url_args: vm.acWithdrawSubmitData
              });
            } else {
              // 提交成功，设置日历。
              console.log(vm.nodeCode.CALENDAR_NODE_W, product.days, res.loanId);
              // setCalendar(vm.nodeCode.CALENDAR_NODE_W, product.days, res.loanId);
            }
            if (vm.$route.query.repaymentDetail) { // 返回还款页
              vm.$router.go(-1);
            } else {
              localStorage.removeItem('statusDetail');
              // 针对有增信标签且未补充资料的用户, 从surveyActivity返回不缓存标识
              if (this.$route.query.surveyActivity && !this.canShowAddPersonalInformation) {
                localStorage.setItem('surveyActivity', 'surveyActivity')
              }

              vm.$router.push({ path: "/withdrawStatus", query: {
                data: JSON.stringify({
                        status: res.mutuallyExclusive === -1 ? true : false,
                        loanAmount: parseInt(vm.loanDetail.actualTotalAmount),
                        bankDetail: vm.loanDetail.bankAcctList[vm.bankIndex],
                        loanTerm: data.loanTerm,
                        repaymentDetailList: vm.loanDetail.repaymentDetailList,
                        orderTime: vm.loanDetail.repayment.orderTime,
                        dueDate: vm.loanDetail.repayment.deadline
                      })
              }});
            }
          } else {
            vm.showSubmitDialog = true;
            vm.showMsg = 'You still have some outstanding orders on other platform. Please try again after you pay them off.';
            vm.retPin();
            vm.showPin = false;
            vm.resetOtp()
            vm.showOtpVerifyPop = false;
            vm.captcha = ''
            if (vm.deviceType === 'AC') {
              vm.acWithdrawPageReport('withdraw_submit_unvalid');
            }
          }
          vm.withdrawReport(`mutually_exclusive_result_${res.mutuallyExclusive}`);
          resolve(res);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          console.log('loanApply4merge', e);
          vm.withdrawReport(`loan_apply_submit_error_${vm.withdrawProcessNum}`);
          if (e.freezeMsg || e.msg) {
            vm.$toast({
              message: e.freezeMsg || e.msg
            });
            // 用户冻结
            if (e.freezeMsg) {
              vm.withdrawReport(`current_user_frozen_success`);
            }
          }
          if (e.code === 1502005) {
            vm.withdrawReport(`error_in_password`);
          }
          if (e.code) {
            vm.withdrawReport(`submit_error_${e.code}_${vm.withdrawProcessNum}`);
          }
          vm.retPin(1);
          vm.$refs['input0'] && vm.$refs['input0'][0].focus();


          if (vm.deviceType === 'AC') {
            vm.acWithdrawPageReport('withdraw_submit_unvalid', `${e.code}:${e.msg}`.substring(0,200));
          }

          // 校验pin错误
          if (e.code === 1502005) {
            this.queryOtp('auto')
          } else {
            this.showPin = false
            reject(e);
          }

          if (e.code === 1208005 && this.checkDeviceStage === 'OTP') {
            // OTP错误时
            // 重置验证状态
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'N');
          }
          
        });
      })
    },
    // 获取当前用户的总借款列表。
    queryLoanList4nc() {
      let vm = this;
      console.log('api.commonBlc.queryLoanList4nc', api.commonBlc.queryLoanList4nc);
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryLoanList4nc, {
          data:{
              "loanStatus":"a",
              "term":0
          },
          page:{
              "isNext":false,
              "pageNum":0,
              "pageSize":1,
              "startIndex":0,
              "totalPage":0,
              "totalRecord":0
          }
        }).then(res => {
          if (res.loanList.length > 0) {
            vm.hasLoan = true;
            // 已有借款，则设为af已上报
            localStorage.setItem('applyAppsflyerData', 1);
          }
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    // 获取活动信息
    queryActivityInfo() {
      let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryActivityInfo, {
          data:{
            activityName: 'palmcredit7thActivity'
          }
        }).then(res => {
          console.log('queryActivityInfo==res', res)
          // "invalid或者是Valid"
          this.activityStatus = res.activityStatus;
          localStorage.setItem('activityStatus', this.activityStatus);
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    getNow() {
      const vm = this;
      vm.showBackDialog = false;
      vm.withdrawReport(`get_now_button_click`);
    },
    // 交易事件触发(必须调用后才能发起提现。)
    inputPin(e, index) {
      const key = e.keyCode;
      const vm = this;
      let value = vm.pinList[index].value;
      if (value === "" || value === " " || isNaN(value)) { // 处理.+-这类符号
        e.target.value = '';
      }
      if (key == 8 && index > 0) {
        vm.pinList[index].showInput = false;
        vm.pinList[index - 1].value = '';
        vm.pinList[index - 1].showInput = true;
        vm.$refs['input' + (index - 1)][0].focus();
      } else if (vm.pinList[index] !== '' && index < 3) {
        if (vm.pinList[index].value !== '') {
          vm.$refs['input' + (index + 1)][0].focus();
          vm.pinList[index].showInput = false;
          vm.pinList[index + 1].showInput = true;
        }
      } else if (index == 3) {
        if (vm.pinList[3].value !== '') {
          let transactionPassword = '';
          vm.pinList.forEach((item, index) => {
            let value = item.value.slice(0, 1);
            // 防止输入多个数值
            vm.pinList[index].value = value;
            transactionPassword += value;
          });
          vm.transactionPassword = transactionPassword;
          // console.log('transactionPassword', vm.pinList, transactionPassword);
          this.captcha = ''
          this.resetOtp()
          vm.debounceSubmit();
        }
      }
    },
    otpKeyUp(e, index) {
      const key = e.keyCode;
      const vm = this;
      let value = vm.otpList[index].value;
      if (value === "" || value === " " || isNaN(value)) { // 处理.+-这类符号
        e.target.value = '';
      }
      if (key == 8 && index > 0) {
        vm.otpList[index].showInput = false;
        vm.otpList[index - 1].value = '';
        vm.otpList[index - 1].showInput = true;
        vm.$refs['inputOtp' + (index - 1)][0].focus();
      } else if (vm.otpList[index] !== '' && index < 3) {
        if (vm.otpList[index].value !== '') {
          vm.$refs['inputOtp' + (index + 1)][0].focus();
          vm.otpList[index].showInput = false;
          vm.otpList[index + 1].showInput = true;
        }
      } else if (index == 3) {
        if (vm.otpList[3].value !== '') {
          let tmpOtp = '';
          vm.otpList.forEach((item, index) => {
            let value = item.value.slice(0, 1);
            // 防止输入多个数值
            vm.otpList[index].value = value;
            tmpOtp += value;
          });
          vm.captcha = tmpOtp;
          console.log('vm.otpList', vm.otpList, 'captcha', this.captcha);
          vm.debounceSubmitOtp();
        }
      }
    },
    
    debounceSubmitOtp: debounce(function() {
      const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
      this.resetOtp()
      // 未开户的goldman走预提现
      if (chooseBankAcct.bankCode === '090574' && !chooseBankAcct.bankAcctNo) {
        this.goNotGoldmanSubmit('');
      } else {
        this.ninCheckCb = () => {
          if (this.checkDeviceStage === 'OTP') {
            // 输入后设置为通过，具体校验让后台处理
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'Y');
            console.log('this.checkDeviceStageResult closeOtpVerifyPop', this.checkDeviceStageResult);
            this.closeOtpVerifyPop(1);
            this.goWithdraw(9);
          } else {
            this.startLoanApply('');
          }
        }
        this.judgeNinCheck();
      }
      this.withdrawReport(`input_otp_success`);
    }, 2000),
    debounceSubmit: debounce(function() {
      getAesString(this.transactionPassword).then(password => {
        this.transactionPassword = '';
        const chooseBankAcct = this.loanDetail.bankAcctList[this.bankIndex];
        this.retPin();
        // 未开户的goldman走预提现
        if (chooseBankAcct.bankCode === '090574' && !chooseBankAcct.bankAcctNo) {
          this.showPin = false;
          this.goNotGoldmanSubmit(password);
        } else {
          this.ninCheckCb = () => {
            this.startLoanApply(password);
          }
          this.judgeNinCheck();
        }
        this.withdrawReport(`input_pin_success`);
      })
    }, 2000),
    startLoanApply(transactionPassword) {
      const vm = this;
      let product = vm.loanDetail.productList[vm.longActive];
      let productFee = product.productFee[0];
      let amount = vm.getAmount();
      if (amount <= 0) {
        this.showPin = false;
        vm.withdrawReport(`withdraw_process_amount_error2_${vm.withdrawProcessNum}`);
        vm.$toast({
          message: `<div style="width: 240px">Please enter withdraw amount</div>`,
          type: 'html'
        });
        return;
      }
      if (amount < vm.chooseTypeData.minAmount || amount > vm.chooseTypeData.maxAmount) {
        this.showPin = false;
        vm.withdrawReport(`loan_apply_amount2_${amount}_min_${vm.chooseTypeData.minAmount}_max_${vm.chooseTypeData.maxAmount}_${vm.withdrawProcessNum}`);
        vm.$toast({
          message: `<div style="width: 240px">Withdraw amount is ₦${vm.chooseTypeData.minAmount}-₦${vm.chooseTypeData.maxAmount}</div>`,
          type: 'html'
        });
        return;
      }
      let data = {
        bankAccNo: !this.isWalletWithdrawalUser ? vm.loanDetail.bankAcctList[vm.bankIndex].bankAcctNo : '',
        bankCode: !this.isWalletWithdrawalUser ? vm.loanDetail.bankAcctList[vm.bankIndex].bankCode : vm.loanDetail.bankAcctList[vm.bankIndex].verifyType,
        channel: "Flutterwave",
        couponId: vm.coupon && vm.coupon.couponId ? vm.coupon.couponId : '',
        deviceId: vm.deviceId,
        isHasRewardWithdraw: "N",
        ischeckCard: vm.ischeckCard(), // 绑卡可跳过情况下，传false，否则传true
        latitude: vm.gpsInfo.latitude,
        loanAmt: amount,
        loanPurpose: vm.loanDetail.purposeList[vm.purposeActive],
        loanSpan: product.borrowCount,
        loanTerm: productFee.term,
        loanType: product.loanType,
        longitude: vm.gpsInfo.longitude,
        productFeeId: productFee.productFeeId,
        productId: product.productId,
        rewardWithdrawAmt: 0,
        transactionPassword: transactionPassword ? transactionPassword : '', // 输入的pin码在这里一并提交。
        txnScene: product.txnScene,
        wifi: vm.wifiList,
        secondCustLmt: vm.inTimeAreaCal('reduceWithdrawalLimit', 48),
        enableInsurance: productFee.insuranceRate && productFee.insuranceRate.feeRate > 0 ? vm.checkShow : '', // 是否勾选保险费
        reloan: vm.reloan,
        walletAccountNumber: !this.isWalletWithdrawalUser ? '' : vm.loanDetail.bankAcctList[vm.bankIndex].walletAccountNumber, // nin用户要传这个值
        isSupportBalance: this.isSupportBalance, // 当前设备是否支持goldman
        couponTriggerScene: vm.withdrawProcessNum === 2 ? 'RETAIN' : '', // 若提现点击来源于挽留弹窗的get now, 触发后台发券逻辑
        captcha: this.captcha, // otp
      }
      // 人脸验证类型
      if (this.checkDeviceStage === 'UF') {
        data.facePassFlag = 'Y';
      }
      // 使用otp验证类似
      if (this.checkDeviceStage === 'OTP') {
        data.deviceCheckCaptcha = this.captcha;
      }
      vm.loanApply4nc(data);
    },
    // 银行卡状态
    ischeckCard() {
      let vm = this;
      return !vm.cardSkip ? true : false;
    },
    resetOtp() {
      this.otpList = [{
        value: '',
        showInput: true
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }]
    },
    // pin重置
    retPin() {
      let vm = this;
      vm.pinList = [{ // pinList渲染列表
        value: '',
        showInput: true
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }]
    },
    changeBank() {
      this.withdrawReport('withdrawpage_changebankaccount_button_click');
      this.withdrawReport('withdraw_more_button_click');
      this.showCardList = true;
    },
    openFaq(name) {
      this.$refs.faqPopupDom.openFaq(name)
    },
    openFaqGoldman() {
      let account = '';
      if (this.goldManAccount) {
        const bankAcctNo = this.goldManAccount.bankAcctNo;
        account = bankAcctNo ? `(${bankAcctNo.slice(-4, bankAcctNo.length)})` : ''
      }
      this.faqGoldmanSetting = {
        title: 'Where disbursed?',
        account: account,
        button: 'OK'
      };
      this.$refs.faqGoldmanDom.openFaq()
    },
    tipConfirm() {
      this.showRelonTips = false;
    },
    /**
     * 计算优惠券是否可用
     * @param {Number} mLoanSpan 借款天数（目前分期没有使用优惠券）。
     * @param {Number} mLoanAmt 借款数额。
     * **/
    calculateCouponCanUse(coupon, mLoanSpan, mLoanAmt) {
      if (!coupon) {
        return false;
      }
      let atLeastAmt = 0;
      if (coupon.reducedAmount != null) {
        atLeastAmt = coupon.reducedAmount;
      }
      let atLeastDay = coupon.reducedDays;
      let isDateAvailable = coupon.status === 'UNUSED' ? true : false;
      if (atLeastAmt <= mLoanAmt && atLeastDay <= mLoanSpan && isDateAvailable) { // 满足满减条件，生效日期。
        return true;
      } else {
        return false;
      }
    },
    cancelBack() {
      let vm = this;
      if (vm.$route.query.repaymentDetail) {
        vm.$router.back(-1);
      } else {
        vm.withdrawReport(`cancel_button_click`);
        vm.showBackDialog = false;
        gotoHomeActivity('index');
      }
    },
    myBackFun() {
      // 若用户点击物理键返回，则关闭当前nincheck弹窗，弹出提现页的挽留弹窗
      if (this.$refs.ninCheck.showNinCheck) {
        this.$refs.ninCheck.hideCheck();
      }
      if (!this.disableBackPopup) {
        if (inTimeAreaCal('withdraw_discount_A01', 24 * 7) && !inTimeAreaCal('withdraw_discount_A01_BOX_WORK', 24)) {
          // 当前是withdraw_discount_A01_BOX弹窗，且24小时内未弹出过
          this.giftDialogType = 'withdraw_discount_A01';
          localStorage.setItem('withdraw_discount_A01_BOX_WORK', new Date().getTime());
          this.giftDialogActivityText = 'Congrats ! You have received a <span>10% interest discount</span>, go and use it</span>';
          this.showGiftDialog = true;
          this.withdrawReport('withdraw_interest_discount_popup_view');
        } else if (inTimeAreaCal('withdraw_increase_B01', 24 * 7) && !inTimeAreaCal('withdraw_increase_B01_BOX_WORK', 24)) {
          // 当前是withdraw_increase_B01_BOX弹窗，且24小时内未弹出过
          this.giftDialogType = 'withdraw_increase_B01';
          localStorage.setItem('withdraw_increase_B01_BOX_WORK', new Date().getTime());
          this.giftDialogActivityText = 'Congrats ! You have received a <span>6% credit increase</span> coupon, use it now</span>';
          this.showGiftDialog = true;
          this.withdrawReport('withdraw_credit_increase_popup_view');
        } else {
          this.showGiftPopup = true;
        }
      } else {
        gotoHomeActivity('index');
      }
      this.withdrawReport('withdraw_popup_one_activity');
      this.withdrawReport(`back_button_click`);
    },
    hideDialog() {
      let vm = this;
      vm.showSubmitDialog = false;
      gotoHomeActivity('index');
    },
    hideBackPopup(type) {
      this.showBackPopup = false;
      if (type === 'close') {
        this.withdrawReport('interest_down_off_click');
        gotoHomeActivity('index');
      } else {
        this.withdrawReport('interest_down_getnow_click');
      }
    },
    hideGiftPopup(type) {
      // 点击Get Now调用提现的逻辑
      if (type === 'confirm') {
        if (this.purposeActive === -1) {
          this.$toast('Please select the Purpose of Loan first');
          return;
        }
        this.showGiftPopup = false;
        this.goWithdraw(2);
        if (this.stepGiftPopup === '1') {
          this.withdrawReport('withdraw_popup_one_getnow_click');
        } else {
          this.withdrawReport('withdraw_popup_two_getnow_click');
        }
      } else if (type === 'close' && this.stepGiftPopup === '1') {
        this.stepGiftPopup = '2';
        this.withdrawReport('withdraw_popup_two_activity');
      } else if (type === 'close' && this.stepGiftPopup === '2') {
        this.showGiftPopup = false;
        this.stepGiftPopup = '1';
        this.withdrawReport('withdraw_popup_two_off_click');
        console.log('stepGiftPopup2')
        // 每天最多只弹出1次（即用户不选择原因，仅关闭弹窗，则当天不再弹出），用户提交原因后7天内不再弹调研弹窗
        if (!this.inTimeAreaCal('onlyCloseLimit', 24) && !this.inTimeAreaCal('reasonSubmitCloseLimit', 24 * 7)) {
          this.reasonType = 'back';
          this.showReasonPopup = true;
          this.disableBackPopup = true;
          this.withdrawReport('withdraw_popup_reason_activity');
        } else {
          gotoHomeActivity('index');
        }
      }
    },
    forgetPin() {
      this.$router.push({ path: '/forgetPin'});
    },
    withdrawReport(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'index',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // 调用原生绑银行账户
    nativeBindBankAccount() {
      let vm = this;
      window.bindBankAccountCb = function (res) {
        console.log(JSON.parse(vm.$decode(res)));
        let result = JSON.parse(vm.$decode(res)).result;
        if (result === 'success') {
          vm.withdrawReport('native_bind_bank_account_success');
          if (vm.loanDetail.bankCardList.length === 0) {
            // 刷新银行账户列表再预提现
            vm.queryWithdrawAcctInfoCutoverSelect4nc().then(() => {
              if (!vm.cardSkip) { // 若为非绑卡跳过状态，则发起预提现
                vm.loanApplyNoBindCardFlag = true;
                vm.loanApplyNoBindCardInit();
              }
              // 初次绑银行账户后, 跳转绑卡
              if (vm.loanDetail.bankAcctList.length === 1) {
                vm.addBank(1);
              }
            });
          } else {
            vm.loanApplyNoBindCardFlag = false;
            vm.addBankFromWithdraw = false;
          }
        } else {
          // 处理原生重复绑卡的情况
          if (vm.loanDetail.bankCardList.length === 0) {
            // 刷新银行账户列表再预提现
            vm.queryWithdrawAcctInfoCutoverSelect4nc().then(() => {
              if (!vm.cardSkip) { // 若为非绑卡跳过状态，则发起预提现
                vm.loanApplyNoBindCardFlag = true;
                vm.loanApplyNoBindCardInit();
              }
              // 初次绑银行账户后, 跳转绑卡
              if (vm.loanDetail.bankAcctList.length === 1) {
                vm.addBank(1);
              }
            });
          }
          vm.addBankFromWithdraw = false;
        }
      }
      vm.withdrawReport('native_bind_bank_account_start');
      gotoBindBankAccount('bindBankAccountCb');
    },
    // 调用原生绑卡
    nativeBindBankCard() {
      let vm = this;
      let product = vm.loanDetail.productList[vm.longActive];
      let productFee = product.productFee[0];
      window.bindBankCardCb = function (res) {
        console.log(JSON.parse(vm.$decode(res)));
        let result = JSON.parse(vm.$decode(res)).result;
        if (result === 'success' || result === 'success_skip') {
          if (result === 'success_skip') {
            vm.withdrawReport('native_bind_bank_card_success_skip');
          } else {
            vm.withdrawReport('native_bind_bank_card_success');
          }
          if (vm.loanApplyNoBindCardFlag) { // 预提现时，处于绑卡成功/绑卡跳过状态：跳转到提现结果页。
            vm.$router.push({ path: "/withdrawStatus", query: {
              data: JSON.stringify({
                      status: res.mutuallyExclusive === -1 ? true : false,
                      loanAmount: parseInt(vm.loanDetail.actualTotalAmount),
                      bankDetail: vm.loanDetail.bankAcctList[vm.bankIndex],
                      loanTerm: productFee.term,
                      repaymentDetailList: vm.loanDetail.repaymentDetailList,
                      orderTime: vm.loanDetail.repayment.orderTime,
                      dueDate: vm.loanDetail.repayment.deadline
                    })
            }});
          } else {
            // 刷新银行卡列表
            vm.queryCustomerBankCardList4nc();
            if (result === 'success_skip') {
              vm.cardSkip = true;
            } else {
              vm.haveCard = true;
            }
            if (vm.addBankFromWithdraw) { // 只有点击提现后的绑卡，才会自动提交。
              vm.goWithdraw(3);
            }
          }
        } else {
          vm.queryCustomerBankCardList4nc().then(() => { // 即使是失败的情况下，有可能处于绑卡pending状态，所以返回还是查询一次银行卡看看是否需要提交。
            if (vm.loanDetail.bankCardList.length > 0 && vm.addBankFromWithdraw) {
              vm.goWithdraw(4); // 自动重新触发提现。
            }
          })
        }
      }
      gotoBindBankCard('bindBankCardCb');
    },
    // 添加银行账户确认
    confirmAddBankAcc() {
      let vm = this;
      vm.showDialog = false;
      vm.withdrawReport('withdrawpage_toaddone_button_click');
      vm.addBank(2);
    },
    goActivateTradeBigdata() {
      let vm = this;
      getDeviceId().then(deviceId => {
        vm.deviceId = deviceId;
        // 需要在获取到设备id后才发起请求
        vm.getDeviceVerification();
      });
      getWifiList().then(wifiList => {
        vm.wifiList = wifiList || '';
      })
      // console.log('vm.deviceId', vm.deviceId);
      if (vm.deviceType !== 'AC') {
        window.getGpsInfoCb = function (res) {
          console.log('getGpsInfoCb', res);
          vm.gpsInfo = JSON.parse(vm.$decode(res));
        }
        getGpsInfo('getGpsInfoCb'); // 回调
        existedPin().then(isPin => {
          vm.isPin = isPin;
        });
        // 需要调用这个接口之后，才可以正常提现。
        let uid = guid();
        vm.activeUid = uid;
        console.log('getCustIdStart')
        getCustId().then(custId => {
          console.log('getCustIdCb', custId, typeof custId);
          if (custId && custId !== 'undefined') {
            // 无bvn用户会存在没有custId的情况，若存在custId，则需要设置activeUid为空，避免重复调用。
            vm.activeUid = '';
            activateTradeBigdata(uid, custId, 'activateTradeBigdataCb');
          }
        });
        window.activateTradeBigdataCb = function(res) {
            let result = JSON.parse(vm.$decode(res));
            console.log('activateTradeBigdataCb', result, result.activateState === 'success');
            if (result.activateState === 'success') {
              vm.activateState = true;
            } else {
              vm.activateState = false;
              vm.$toast({
                message: result.errMsg
              })
            }
          }
      } else {
        if (window.dopplerLib && window.dopplerLib.getGpsInfo) { // 新的GPS原生能力。
          window.getGpsInfoCb = function (res) {
            console.log('getGpsInfoCb', res);
            vm.gpsInfo = JSON.parse(vm.$decode(res));
            console.log('gpsInfo', vm.gpsInfo);
          }
          getLocation('getGpsInfoCb'); // 回调
        } else {
          vm.gpsInfo = JSON.parse(getLocation());
        }
        let batchNo = getBatchNo();
        let custId = vm.userInfor.custId;
        console.log(batchNo, custId);
        activateTradeBigdataAC(batchNo, custId);
        window.setActivateTradeResult = (res) => {
            console.log('activateTradeBigdataRes', res, JSON.stringify(res));
            if (res === 'true') {
              console.log('test');
            } else {
              vm.$toast({
                message: res.errMsg
              })
            }
        }
      }
    },
    // 批量埋点上报（避免堵塞主流程）
    startBPoint() {
      let vm = this;
      vm.startBuriedPoint.forEach(item => {
        vm.withdrawReport(item);
      });
    },
    checkAcc() {
      let vm = this;
      vm.checkShow = !vm.checkShow;
      vm.startLoanCalculate();
    },
    // 预提现订单接口
    loanApplyNoBindCardInit(transactionPassword) {
      const vm = this;
      let product = vm.loanDetail.productList[vm.longActive];
      let productFee = product.productFee[0];
      if (product.isPcx) { // 只对NC系列添加预提现
        vm.loanApplyNoBindCardFlag = false;
        return Promise.resolve();
      }
      vm.$loading();
      let data = {
        bankAccNo: vm.loanDetail.bankAcctList[vm.bankIndex].bankAcctNo,
        bankCode: vm.loanDetail.bankAcctList[vm.bankIndex].bankCode,
        channel: "Flutterwave",
        couponId: vm.coupon ? vm.coupon.couponId : '',
        deviceId: vm.deviceId,
        isHasRewardWithdraw: "N", // 写死
        ischeckCard: vm.ischeckCard(),
        latitude: vm.gpsInfo.latitude,
        loanAmt: vm.getAmount(),
        loanPurpose: vm.loanDetail.purposeList[vm.purposeActive],
        loanSpan: product.borrowCount,
        loanTerm: productFee.term,
        loanType: product.loanType,
        longitude: vm.gpsInfo.longitude,
        productFeeId: productFee.productFeeId,
        productId: product.productId,
        rewardWithdrawAmt: 0, // 写死
        transactionPassword: transactionPassword ? transactionPassword : '', // 输入的pin码在这里一并提交。预提现过程中，提交后代就校验
        txnScene: product.txnScene,
        wifi: vm.wifiList,
        secondCustLmt: vm.inTimeAreaCal('reduceWithdrawalLimit', 48),
        reloan: vm.reloan,
        enableInsurance: productFee.insuranceRate && productFee.insuranceRate.feeRate > 0 ? vm.checkShow : '', // 是否勾选保险费
        isSupportBalance: this.isSupportBalance, // 当前设备是否支持goldman
        couponTriggerScene: vm.withdrawProcessNum === 2 ? 'RETAIN' : '' // 若提现点击来源于挽留弹窗的get now, 触发后台发券逻辑
      };
      // 人脸验证类型
      if (this.checkDeviceStage === 'UF') {
        data.facePassFlag = 'Y';
      }
      // 使用otp验证
      if (this.checkDeviceStage === 'OTP') {
        data.deviceCheckCaptcha = this.captcha;
      }
      return  new Promise((resolve, reject) => {
        vm.$http(api.withdraw.loanApplyNoBindCardInit, {
          addHeader: vm.deviceType === 'AC' ? vm.addHeader : {},
          showErrorToast: false,
          data: data
        }).then(res => {
          this.captcha = '';
          // 缓存借款后的状态: 用于绑卡后返回的显示。
          localStorage.setItem('loanApplyNoBindCardInitData', JSON.stringify({
            status: true,
            loanAmount: parseInt(vm.loanDetail.actualTotalAmount),
            bankDetail: vm.loanDetail.bankAcctList[vm.bankIndex],
            loanTerm: productFee.term,
            repaymentDetailList: vm.loanDetail.repaymentDetailList,
            orderTime: vm.loanDetail.repayment.orderTime,
            dueDate: vm.loanDetail.repayment.deadline
          }));
          vm.startAppsflyerData();
          // 若为原生绑账户的加载，则需要隐藏loading。
          if (vm.APPConfig.bindBankAccountPageFlag !== 'Y') {
            vm.$hideLoading();
          }
          // 预提现接口不需要处理互斥等逻辑。
          vm.withdrawReport('loan_apply_no_bind_card_init_success');
          resolve(res);
        }).catch(e => {
          if (e.code === 1208005 && this.checkDeviceStage === 'OTP') {
            // OTP错误时
            this.$newToast(e.msg);
            // 重置验证状态
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'N');
          }
          // 预提现接口不需要处理报错
          reject(e);
        });
      })
    },
    // 合并查询客户银行账户，卡号等信息
    queryCustCardInfo() {
      return  new Promise((resolve, reject) => {
        this.$http(api.withdraw.queryCustCardInfo, {
          data: {
            isSupportBalance: this.isSupportBalance
          }
        }).then(res => {
          console.log('queryCustCardInfo===res', res)
          this.loanDetail.bankCardList = res.blcCustBankCardListResp ? res.blcCustBankCardListResp.bankCardList : [];
          if (res.withdrawAcctResp && res.withdrawAcctResp.bankAcctList && res.withdrawAcctResp.bankAcctList.length > 0) {
            let isWalletWithdrawalUser = false;
            res.withdrawAcctResp.bankAcctList.forEach(item => {
              if (item.walletAccountNumber) {
                item.bankAcctNo = item.walletAccountNumber;
                item.bankName = item.verifyType;
                isWalletWithdrawalUser = true;
              }
            })
            this.isWalletWithdrawalUser = isWalletWithdrawalUser;
            if (this.isWalletWithdrawalUser) { // 缓存用户账户类型
              localStorage.setItem('userAccountType', 'isWalletWithdrawalUser');
            } else {
              localStorage.setItem('userAccountType', '');
            }
          } else {
            res.withdrawAcctResp.bankAcctList = [];
          }
          this.loanDetail.bankAcctList = res.withdrawAcctResp ? res.withdrawAcctResp.bankAcctList : [];
          this.defaultLoanAccount = res.defaultLoanAccountResp;
          this.goldmanBankAcctType = res.goldmanBankAcctType;
          this.enableGoldman = res.enableGoldman;
          this.goldmanDebitScene = res.goldmanDebitScene
          this.cardSkip = res.blcCustBankCardListResp ? res.blcCustBankCardListResp.skip : false;
          let haveGoldmanAccount = false;
          this.loanDetail.bankAcctList.forEach((item) => {
            if (item.bankCode === "090574") {
              haveGoldmanAccount = true;
            }
          });
          // 用户处于goldman范围时，无goldman账户，前端默认添加goldman账户。
          if (this.enableGoldman && !haveGoldmanAccount) {
            this.loanDetail.bankAcctList.unshift({
              bankCode: "090574",
              bankName: 'Goldman MFB',
              bankAcctNo: '',
              channel: null,
              bankAcctName: null,
              walletAccountNumber: null,
              verifyType: null
            })
          }
          if (this.defaultLoanAccount && this.defaultLoanAccount.bankCode) {
            // 上一笔其他绑定的非goldman索引
            let latestAccountFlagIndex = 0;
            // 是否已经设置默认账户
            let haveSetdefaultLoanAccount = false;
            this.loanDetail.bankAcctList.forEach((item, index) => {
              // 存在账户且bankAcctNo不为空。
              if (item.bankAcctNo && item.bankAcctNo === this.defaultLoanAccount.bankAcctNo) {
                this.bankIndex = index;
                haveSetdefaultLoanAccount = true;
              }
              if (item.latestAccountFlag === 'Y') {
                latestAccountFlagIndex = index;
              }
              if (item.bankCode === "090574") {
                this.goldManAccount = item;
              }
            });
            // 未设置默认账号时
            if (!haveSetdefaultLoanAccount) {
              // 使用上一笔其他绑定的非goldman索引作为默认账号，若不存在，就默认第一个为默认账号
              this.bankIndex = latestAccountFlagIndex;
            }
          }
          console.log('queryCustCardInfo');
          resolve(res);
        }).catch(e => {
          // 预提现接口不需要处理报错
          reject(e);
        });
      })
    },
    // 合并查询客户相关的产品信息
    queryCustProductInfo(type) {
      const vm = this;
      return new Promise((resolve, reject) => {
        this.$http(api.withdraw.queryCustProductInfo, {
          data: {
            blcQueryCouponListReq: {
              status: 'UNUSED',
              useScene: '1',
              isNext: false,
              pageNum: 0,
              pageSize: 100,
              startIndex: 0,
              totalPage: 0,
              totalRecord: 0
            },
            queryProductWithSerV5Req: {
              isReloanApply: false,
              couponId: type === 'imitUpConpon' && this.coupon ? this.coupon.couponId : ''
            }
          },
          showErrorToast: false
        }).then(res => {
          console.log('queryCustProductInfo===res', res)
          console.log('type', type)
          // 处理产品额度
          vm.loanDetail.usableLmtAmt = res.queryCustLmtResp.usableLmtAmt || res.queryCustLmtResp.lmtAmt;
          // res.usableLmtAmt会存在为0的情况，这时候使用哪个？， 默认进来显示全部
          vm.loanDetail.amount = vm.thousands(vm.loanDetail.usableLmtAmt);
          // 处理优惠券
          vm.couponList = res.queryCouponListResp.couponList ? res.queryCouponListResp.couponList : [];
          // 获取基本配置
          vm.APPConfig = res.blcQueryLoanAPPConfigResp;
          vm.lastPaidOffLoan = res.lastPaidOffLoan ? res.lastPaidOffLoan : {};
          vm.startBuriedPoint.push('query_cust_lmt_for_multi_term_success');
          // 产品处理
          vm.dealProductInfo(res.querySimpleProductResp);
          if (type === 'init') {
            // 缓存初始的产品列表
            vm.loanDetail.temProductList = JSON.parse(JSON.stringify(vm.loanDetail.productList));
          }
          resolve(res);
        }).catch(e => {
          // 无产品需要提示弹窗。
          if (e.code === 1505016) {
            vm.popTips = e.msg;
            vm.showNoProDialog = true;
          } else {
            vm.$toast(e.msg);
          }
          reject(e);
        });
      })
    },
    // 处理产品信息
    dealProductInfo(querySimpleProductResp) {
      const vm = this;
      let allProductList = querySimpleProductResp && querySimpleProductResp.productList;
      vm.reloan = querySimpleProductResp.isReloan;
      let maxAmount = 0;
      let have28 = false;
      if (allProductList && allProductList.length > 0) {
        allProductList.forEach((item) => {
          let productFee = item.productFee[0];
          if (maxAmount < productFee.maxAmt) {
            maxAmount = productFee.maxAmt;
          }
          if (productFee.term === 1 && item.borrowCount === 28) {
            have28 = true;
          }
          item.days = productFee.totalBorrowCount;
          item.type = `${item.days} days`;
          item.rate = productFee.dayRate * 100;
          if (item.productFee[0].term > 1) {
            item.tips = `Every ${ item.borrowCount } days in ${item.productFee[0].term} installment`;
          }
          vm.withdrawReport(`${item.type.replace(/\s/g, '_')}_tenor_button_view`);
          return item;
        });
        vm.maxUsableLmtAmt = maxAmount;
        let pcSingleProductList = [];
        let pcMuiltiProductList = allProductList.filter(item => {
          if (item.productFee[0].term === 1) {
              pcSingleProductList.push(item);
          }
          return item.productFee[0].term > 1;
        });
        // 单期排序
        pcSingleProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        // 多期排序
        pcMuiltiProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        allProductList = [...pcMuiltiProductList, ...pcSingleProductList];
        // 分开保存单多期产品
        this.singleProductList = pcSingleProductList;
        this.muiltiProductList = pcMuiltiProductList
        // 找到默认产品
        vm.proIndex = allProductList.findIndex((item) => item.productFee[0].productFeeId === querySimpleProductResp.defaultFeeId);
        if (vm.proIndex < 0) { // 处理只有一个产品的情况
          vm.proIndex = 0;
        } else {
          let defaultProduct = allProductList[vm.proIndex];
          console.log(defaultProduct)
          // 默认产品是14*2产品,且存在28天单期产品，且用户上一笔结清不是是14*2
          if (defaultProduct.productFee[0].term === 2 && defaultProduct.borrowCount === 14 && have28 &&
          !(vm.lastPaidOffLoan && vm.lastPaidOffLoan.loanTerm === 2 && vm.lastPaidOffLoan.loanSpan === 14)) {
            vm.tag14and2User = true;
            if (vm.proIndex !== 0) {
              vm.proIndex = 0;
            } else {
              vm.proIndex = 1;
            }
          }
          // ios本产品新客默认选择7D单期产品
          if (judgeClient() === 'IOS' && vm.isNew) {
            const proIndex = allProductList.findIndex((item) => (item.productFee[0].term === 1) &&  (item.borrowCount === 7));
            if (proIndex !== vm.proIndex && proIndex != -1) { // 没有7D产品时不要修改。
              vm.proIndex = proIndex;
            }
          }
          // 1针对安卓的PC/NC系列产品，若用户为全平台新客（全平台没有借过款的客户），提现页默认产品改为默认最长期限产品（若多期与单期天数一样，则默认多期）
          if (judgeClient() !== 'IOS' && vm.isAllProductNew && allProductList.length > 0) {
            // 天数从高到低排序
            // 获取单期第一个产品
            const singleDays = pcSingleProductList.length > 0 && pcSingleProductList[0].days;
            // 获取多期第一个产品
            const mulDays = pcMuiltiProductList.length > 0 && pcMuiltiProductList[0].days;
            // 比较两者总天数
            // 多期在总产品数组上面
            if (singleDays > mulDays) {
              vm.proIndex = pcMuiltiProductList.length > 0 ? pcMuiltiProductList.length : 0;
            } if (singleDays <= mulDays) { // 其他情况，无论有无多期，都选择第一个，因为天数高的在第一个
              vm.proIndex = 0;
            }
          }
        }
        console.log('tag14and2User', vm.tag14and2User);
        // 若用户仅支持可见单期时，下方增加固定展示7*4和14*4多期产品
        if (pcMuiltiProductList.length === 0 && pcSingleProductList.length > 0) {
          const lockMuiltiProductList = [{
            tips: 'Every 7 days in 4 installment',
            type: '28 days',
            islock: true,
            productId: '999999999'
          }, {
            tips: 'Every 14 days in 4 installment',
            type: '56 days',
            islock: true,
            productId: '999999998'
          }];
          allProductList = [...allProductList, ...lockMuiltiProductList];
        }
        console.log('vm.proIndex', vm.proIndex);
        vm.loanDetail.productList = allProductList;
        if (vm.proIndex > -1) {
          const defaultProduct = allProductList[vm.proIndex];
          vm.startBuriedPoint.push(`default_product_${defaultProduct.borrowCount}D${defaultProduct.productFee[0].term}T`);
          // remindUpLimit为提升的额度金额*默认产品膨胀系数，若remindUpLimit小于默认产品最大可提现金额，则展示该提额提示，否则不展示
          let remindUpLimit = querySimpleProductResp.remindUpLimit * defaultProduct.productFee[0].lmtRate;
          if (remindUpLimit < defaultProduct.productFee[0].maxAmt) {
            vm.remindUpLimit = remindUpLimit;
          }
        }
        vm.startBuriedPoint.push('query_product_with_ser4nc_success');
      } else {
        vm.startBuriedPoint.push('query_product_with_ser4nc_success_no_product');
      }
    },
    /**
     * 判断是否有过期
     * @param {String} flag 缓存标识
     * @param {Number} time 时间（小时）
    */
    inTimeAreaCal(flag, time) {
      const flagTime = localStorage.getItem(flag);
      const expiredTime = new Date().getTime() - 60 * 60 * 1000 * time;
      // 超出时间后移除
      if (flagTime > 0 && flagTime < expiredTime) {
        localStorage.removeItem(flag);
        return false;
      }
      if (flagTime > 0 && flagTime > expiredTime) {
        return true;
      }
      return false;
    },
    startBack() {
      // 要一直保持可以使用返回键处理。
      window.history.pushState(null, null, document.URL);
      this.myBackFun();
    },
    // 初始化数据
    initData(from) {
      const vm = this;
      console.log('vm.supportedFeatures', vm.supportedFeatures, vm.supportedFeatures.goldbank)
      if (vm.supportedFeatures.goldbank) {
        vm.isSupportBalance = true;
      }
      // 因为客户端的loading跟前端的一致，加载完前端页面会关闭，所以前端的加载要延迟一点。
      setTimeout(() => {
        vm.$loading();
      }, 300);
      // 增加方法并行调用时，要把方法加在最后，避免顺序出错。
      return Promise.all([
        vm.queryCustCardInfo(),
        vm.queryCustProductInfo('init'),
        vm.queryStageInfo(),
        vm.queryNoBindCardInfo(),
        vm.getUserType(),
        vm.getPopAPPConfig()
      ]).then(res => {
        // 有有效预提现订单且无有效提现订单
        // if (res[3].haveTempLoan && vm.loanDetail.bankCardList.length === 0 && !from.name) {
        //   localStorage.setItem('orderConfirm', JSON.stringify(res[3]))
        //   vm.$router.push({
        //     path: "/orderConfirm"
        //   });
        // }
        console.log(res);
        if (vm.deviceType === 'AC') {
          vm.acWithdrawPageReport('withdraw_page_show_done');
        }
        vm.dealAfterInitData(from);
      }).catch(() => {
        vm.dealAfterInitData(from);
      });
    },
    // 处理加载数据后的流程
    dealAfterInitData(from) {
      const vm = this;
      // 埋点上报
      vm.startBPoint();
      // 绑卡都会存在返回首页的情况, 这时候需要自动触发用户提交提现
      if (from.name == 'cardStatus' || from.name == 'checkCardStatus') {
        const widthdrawData = localStorage.getItem('widthdrawData') ? JSON.parse(localStorage.getItem('widthdrawData')) : {};
        const loanApplyNoBindCardInitData = localStorage.getItem('loanApplyNoBindCardInitData');
        console.log('loanApplyNoBindCardInitData', loanApplyNoBindCardInitData, vm.$route.query.cardStatus);
        const type = vm.$route.query.cardStatus;
        setTimeout(() => {
          vm.$hideLoading();
        }, 500);
        if (loanApplyNoBindCardInitData && (type === 'success' || type === 'skip')) { // 存在预提现时，绑卡跳过或者成功则默认跳转提现状态页。
          // 同步移除缓存的数据。
          localStorage.removeItem('loanApplyNoBindCardInitData');
          // 针对有增信标签且未补充资料的用户, 从surveyActivity返回不缓存标识
          if (this.$route.query.surveyActivity && !this.canShowAddPersonalInformation) {
            localStorage.setItem('surveyActivity', 'surveyActivity')
          }
          vm.chooseType(vm.proIndex);
          vm.$router.push({
            path: "/withdrawStatus",
            query: {
              data: loanApplyNoBindCardInitData
            }
          });
        } else {
          localStorage.removeItem('loanApplyNoBindCardInitData');
          vm.loanDetail.amount = widthdrawData.amount;
          vm.longActive = widthdrawData.longActive;
          vm.purposeActive = widthdrawData.purposeActive;
          vm.bankIndex = widthdrawData.bankIndex;
          vm.checkShow = widthdrawData.checkShow;
          vm.$store.commit('SET_COUPON', widthdrawData.coupon);
          vm.chooseType(widthdrawData.longActive, 'code');
          vm.goWithdraw(5);
        }
      } else {
        // 用户进入提现页时，需要根据是否默认goldman的账户以及最小产品期限判断是否需要切换账号
        this.changeDefaultAccount();
        // 默认勾选借款目的
        if (this.queryStageInfoPurpose && this.isNew){
          this.loanDetail.purpose = this.queryStageInfoPurpose;
          this.loanDetail.purposeList.forEach((item, index) => {
            if (item === this.queryStageInfoPurpose) {
              this.purposeActive = index;
            }
          })
        } else if (!this.isNew) {
          // 针对本产品老客（即本产品结清次数≥1的用户），提现页面-贷款目的默认选择business
          this.loanDetail.purpose = 'Business';
          this.loanDetail.purposeList.forEach((item, index) => {
            if (item === this.loanDetail.purpose) {
              this.purposeActive = index;
            }
          })
        }
        console.log('this.couponList', this.couponList);
        let coupon = {};
        if (this.couponList.length > 0) {
          // -用户进入提现页时，判断用户当前有有效期内的场景值为withdraw_discount_A01或withdraw_increase_B01的提现优惠券，则默认勾选最新发放的一张券
          const triggerSceneArr = ['withdraw_discount_A01', 'withdraw_increase_B01'];
          this.couponList.forEach(item => {
            if (triggerSceneArr.includes(item.triggerScene)) {
              if (!coupon.couponId || coupon.couponId < item.couponId) {
                coupon = item;
              }
            }
          });
        }
        if (coupon.couponId) {
          vm.longActive = vm.proIndex;
          console.log('triggerSceneArrCoupon', coupon);
          vm.$store.commit('SET_COUPON', coupon);
          // 普通利息券关闭loading
          if (coupon.triggerScene === 'withdraw_discount_A01') {
            setTimeout(() => {
              vm.$hideLoading();
            }, 500);
          }
        } else {
          // 默认勾选产品
          vm.chooseType(vm.proIndex);
          setTimeout(() => {
            vm.$hideLoading();
          }, 500);
        }
      }
      // 针对有增信标签且未补充资料的用户，每天首次进入提现页时，弹出增信资料弹窗
      // if (!this.inTimeAreaCal('showAddPersonalInformation', 24) && this.canShowAddPersonalInformation
      // && (this.needReCreditContractList !== 'Y' || this.needReCreditliveDetection !== 'Y')) {
      //   this.showAddPersonalInformation = true;
      //   localStorage.setItem('showAddPersonalInformation', new Date().getTime());
      // }
    },
    // 修改默认勾选账户逻辑
    changeDefaultAccount() {
      const singleProductList = this.singleProductList;
      const muiltiProductList = this.muiltiProductList;
      // 天数从高到低排序
      // 获取单期最后一个产品
      const singleDays = singleProductList.length > 0 && singleProductList[singleProductList.length - 1].days;
      // 获取多期最后一个产品
      const mulDays = muiltiProductList.length > 0 && muiltiProductList[muiltiProductList.length - 1].days;
      let proIndex = 0;
      // 比较两者总天数, 最小期限产品（即总天数最小）
      // 多期在总产品数组上面
      if (singleDays > mulDays) {
        proIndex = 0;
      } if (singleDays <= mulDays) { // 单期小于或者等于，都选择单期的
        proIndex = muiltiProductList.length > 0 ? muiltiProductList.length : 0;
      }
      const choseProduct = this.loanDetail.productList[proIndex];
      if (choseProduct) {
        const productFee = choseProduct.productFee[0];
        // 最大可借金额是否<5000, 若小于，默认goldman且存在其他账号时，切换其他账号
        if (productFee.maxAmt < 5000 && this.goldManAccount.bankCode && this.defaultLoanAccount.bankCode === "090574" && this.otherBankAcct.bankCode) {
          this.checkOtherAccout();
        }
      }
    },
    beforeRouteEnter (to, from) {
      const vm = this;
      console.log('beforeRouteEnter', to, from, from.name == 'afterRepaymentLead');
      // 增信用户返回时不提示提醒，弹出增信弹窗
      vm.fromName = from.name;
      if (from.name === 'surveyActivity') {
        if (!this.canShowAddPersonalInformation) {
          vm.$toast('The limit increase will take effect immediately after withdrawal');
        } else {
          // 针对有增信标签且未补充资料的用户, 返回时弹出增信弹窗。
          this.showAddPersonalInformation = true;
        }
      }
      if (from.name === 'setPin' || from.name === 'pinResetStatus') {
        existedPin().then(isPin => {
          console.log('isPin', isPin);
          vm.isPin = isPin;
          vm.goWithdraw(6);
        });
      } else if (from.name === 'addBankAccount') {
        vm.queryWithdrawAcctInfoCutoverSelect4nc().then(() => {
          // 初次绑银行账户
          if (vm.loanDetail.bankAcctList.length > 0 && vm.loanDetail.bankCardList.length === 0) {
            vm.loanApplyNoBindCardInit().then(() => {
              // 跳转添加银行卡
              vm.ninCheckCb = () => {
                vm.addBank(1);
              }
              vm.$hideLoading();
              vm.judgeNinCheck();
            }).catch((e) => {
              // 非设备检测OTP可以正常走
              if (!(e.code === 1208005 && this.checkDeviceStage === 'OTP')) {
                vm.ninCheckCb = () => {
                  vm.addBank(1);
                }
                vm.$hideLoading();
                vm.judgeNinCheck();
              }
            })
          }
        });
      } else if (vm.$route.query.fromName == 'useCoupon' || !from.name || from.name == 'cardStatus' || from.name == 'checkCardStatus' || from.name == 'initPage' || from.name == 'orderConfirm' || from.name == 'afterRepaymentLead'){// 判断从客户端优惠券直接跳转的情况。
        vm.initData(from);
      } else if (from.name == 'updateEmergencyContact' || from.name == 'faceVerification') {
        // 对于需要做设备检测的用户，要先检查设备检测是否通过，再走后续流程
        if (!['N', 'P'].includes(this.checkDeviceStage)) {
          console.log('this.checkDeviceStageResult init', this.checkDeviceStageResult);
          if (this.checkDeviceStageResult === 'Y') {
            vm.goWithdraw(8);
          } else if (this.checkDeviceStage === 'OTP') {
            // 输入后设置为通过，具体校验让后台处理
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'Y');
            this.chooseOtpVerify();
          }
        } else  if (vm.needReCreditContractList === 'N' && vm.needReCreditliveDetection === 'N') {
          // 对于只需要补充资料的用户 从更新紧急联系人或者人脸验证页面返回，检查是否两者都更新完毕。
          // 若更新完毕，触发提现.
          vm.goWithdraw(7);
        }
      }
    },
    chooseAmount() {
      if (this.canShowAddPersonalInformation) {
        this.showAddPersonalInformation = true;
        this.withdrawReport('withdraw_addinfo_choose_amount_click');
      } else {
        this.$toast({
          message: `<div style="width: 220px; word-break: keep-all;">Withdraw now and maintain good credit will open this limit</div>`,
          type: 'html'
        })
      }
    },
    // 触发发送优惠券功能
    queryCreateCoupon(triggerScene) {
      this.$loading();
      return new Promise((resolve, reject) => {
        this.$http(api.coupon.createCoupon, {
          data: {
            triggerScene: triggerScene,
            channel: this.productSource
          }
        }).then(res => {
          this.$hideLoading();
          resolve(res);
        }).catch(e => {
          this.$hideLoading();
          reject(e);
        });
      })
    },
    // 请求CouponList
    queryCouponList4nc(data) {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.coupon.queryCouponList4nc, {
          "data":{"status":"UNUSED", ...data},"page":{"isNext":false,"pageNum":0,"pageSize":100,"startIndex":0,"totalPage":0,"totalRecord":0}
        }).then(res => {
          vm.couponList = res.couponList;
          resolve();
        }).catch(e => {
          reject(e);
        });
      });
    },
    // 获取banner等配置
    getPopAPPConfig() {
      return  new Promise((resolve, reject) => {
        this.$http(api.repayment.popAPPConfig, {
          data: {
            showIncreaseCredit: true, // 查询增信banner状态
            showReCredit: true // 查询是否处于未放款用户且授信通过距今超过180天的（未冻结状态）
          }
        }).then(res => {
          console.log('getPopAPPConfig==配置res', res)
          this.showIncreaseCredit = res.showIncreaseCredit;
          // 针对增信的用户，显示特殊的banner
          if (this.canShowAddPersonalInformation) {
            this.reasonObj.back.list[0].banner = banner1replace;
            this.withdrawReport('withdraw_addinfo_banner_view');
          } else {
            this.reasonObj.back.list[0].banner = banner1;
          }
          if (res.showReCreditConfig) {
            this.$store.commit('SET_NEEDRECREDITCONTRACTLIST', res.showReCreditConfig.needReCreditContractList); // 是否需要更新紧急联系人信息
            this.$store.commit('SET_NEEDRECREDITLIVEDETECTION', res.showReCreditConfig.needReCreditliveDetection);  // 是否需要更新人脸
          }
          resolve(res);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          reject(e);
        });
      });
    },
    startQuestion() {
      // 重置数据
      this.reasonObj.question.list[5].textAreaContent = '';
      this.reasonType = 'question';
      this.showReasonPopup = true;
      this.withdrawReport('withdraw_question_click');
      this.withdrawReport('withdraw_question_pop_view');
    },
    defaultBankAcctImg(bankAcct) {
      if (!this.isWalletWithdrawalUser) {
        if (bankAcct.bankCode === '090574') {
          return this.goldManImg;
        } else {
          return this.backImg
        }
      } else {
        return this.ninBankImg
      }
    },
    //
    verifyGoldmanNIN(data) {
      this.$http(api.commonBlc.verifyGoldmanNIN, {
        data:{
          NINInfo: data
        }
      }).then(() => {
      }).catch(() => {
      });
    },
    closeNinCheck(obj) {
      // 不管怎样关闭，不能堵塞提现流程
      if (obj.type === 'confirm') {
        this.verifyGoldmanNIN(obj.validNIN);
      }
      // 存在回调则处理
      if (this.ninCheckCb) {
        this.ninCheckCb();
        this.ninCheckCb = ''
      }
    },
    goAgree() {
      this.$router.push({path: '/loadUrlPage', query: {
        url: process.env.VUE_APP_AUTHORISATION_DISCLOSURE_URL,
        title: ''
      }});
    },
    goDirectDebit() {
      this.$router.push({path: '/loadUrlPage', query: {
        url: process.env.VUE_APP_DIRECT_DEBIT_URL,
        title: ''
      }});
    },
    checkAccount(type) {
      console.log(type);
      if (type === 'goldman') {
        this.withdrawReport(`withdraw_${this.goldManAccount.bankName}_checkbox_click`);
        if (this.chooseBankAcct.bankCode === this.goldManAccount.bankCode && this.otherBankAcct.bankCode) { // 当前账号为goldman账号，则切换为其他账号
          this.checkOtherAccout();
        } else {
          if (this.goldManAccount.bankCode) { // 未被勾选则勾选
            this.checkGoldmanAccount();
          }
        }
      } else if (type === 'other') { // 当前账号为其他账号，则切换为goldman账号
        this.withdrawReport(`withdraw_${this.otherBankAcct.bankName}_checkbox_click`);
        if (this.chooseBankAcct.bankCode === this.otherBankAcct.bankCode && this.goldManAccount.bankCode) {
          this.checkGoldmanAccount();
        } else { // 未被勾选则勾选
          if (this.otherBankAcct.bankCode) {
            this.checkOtherAccout();
          }
        }
      }
    },
    checkOtherAccout() {
      const index = this.loanDetail.bankAcctList.findIndex(item  => item.bankCode === this.otherBankAcct.bankCode)
      this.bankIndex = index;
    },
    checkGoldmanAccount() {
      const index = this.loanDetail.bankAcctList.findIndex(item  => item.bankCode === this.goldManAccount.bankCode)
      this.bankIndex = index;
    },
    hideAddPersonalInformation(type){
      if (type === 'confirm') {
        this.increaseCredit();
        this.withdrawReport('withdraw_addinfo_popup_submit_click')
      }
    },
    // 提交增信配置
    increaseCredit() {
      this.$loading()
      return  new Promise((resolve, reject) => {
        const uploadDocuments = this.uploadDocuments;
        this.$http(api.commonBlc.increaseCredit, {
          data: {
            salaryProcessUrl: uploadDocuments.salaryFlow[0] && uploadDocuments.salaryFlow[0].id, //工资发放流水照片url
            salaryPayslipUrl: uploadDocuments.salaryPayslip[0] && uploadDocuments.salaryPayslip[0].id, //工资单照片url
            employmentCertificateUrl: uploadDocuments.photoOfWorkBadge[0] && uploadDocuments.photoOfWorkBadge[0].id, //工作证明照片url
            bankAccountBalanceUrl: uploadDocuments.bankAccountBalance[0] && uploadDocuments.bankAccountBalance[0].id, //银行账户照片url
          }
        }).then(res => {
          // 重置初始状态
          this.showAddPersonalInformation = false;
          this.showIncreaseCredit = 'N';
          // 提交增信后，重新刷新页面
          this.initData(this.$parent.from);
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    showAddPersonalInformationBanner() {
      this.showAddPersonalInformation = true;
      this.withdrawReport('withdraw_addinfo_banner_click');
    },
    // 挽留弹框特地选项后
    refreshLoan(triggerScene) {
      this.disableBackPopup = true;
      // 发送优惠券，并刷新优惠券，重新试算
      this.queryCreateCoupon(triggerScene).then(() => {
        if (triggerScene === 'withdraw_discount_A01') {
          this.$newToast('10% interest coupon has been sent to your account, please withdraw');
        } else if (triggerScene === 'withdraw_increase_B01') {
          this.$newToast('6% credit increase coupon has been sent to your account, please withdraw')
        }
        this.queryCouponList4nc({
          "useScene":"1"
        }).then(() => {
          this.startLoanCalculate();
        })
      })
    },
    closeGiftDialog() {
      this.showGiftDialog = false;
    }
  },
  created() {
    let vm = this;
    // 请求额度
    const userInfo = localStorage.getItem("userInfo");
    if (userInfo) {
      vm.userInfo = JSON.parse(userInfo);
    }
    vm.goActivateTradeBigdata();
  },
  mounted() {
    let vm = this;
    this.androidVersion = getOsVersionCode();
    console.log('当前安卓版本(大于28支持选择文件)', this.androidVersion);
    vm.beforeRouteEnter(vm.$parent.to, vm.$parent.from);
    const query = this.$route.query;
    console.log('query', query)
    vm.withdrawReport('withdraw_button_view');
    vm.withdrawReport('withdraw_page_view');
    vm.inTimeAreaCal('reduceWithdrawalLimit', 48);
    vm.inTimeAreaCal('reasonSubmitCloseLimit', 24 * 7);
    vm.inTimeAreaCal('onlyCloseLimit', 24)
    getCurrentAppVersionName().then(currentAppVersionName => {
      vm.currentAppVersionName = currentAppVersionName;
    });
    getCurrentAppVersion().then(appVersion => {
      vm.appVersion = appVersion;
    });
    if (vm.deviceType === 'AC') {
      vm.acWithdrawPageReport('withdraw_page_show');
    }
    vm.afFlag = localStorage.getItem('applyAppsflyerData');
    if (!vm.afFlag) {
      vm.queryLoanList4nc();
    }
    // 如果支持 popstate 一般移动端都支持了
    if (window.history && window.history.pushState) {
      // 往历史记录里面添加一条新的当前页面的url
      window.history.pushState(null, null, document.URL);
    }
    // 请求活动信息
    if (vm.productSource === 'palmcredit' || vm.productSource === 'newcredit') {
      vm.queryActivityInfo();
    }
    vm.withdrawReport('withdraw_question_view');
  },
  destroyed() {
    window.removeEventListener("popstate", this.startBack, false); // false阻止默认事件
  },
  activated() {
    window.addEventListener("popstate", this.startBack, false);
  },
  deactivated() {
    window.removeEventListener("popstate", this.startBack, false); // false阻止默认事件。 不移除则会影响其他页面。
  },
  watch: {
    'loanDetail.amount': function(newValue, oldValue) {
      console.log('loanDetail.amount')
      this.loanDetail.oldAmount = oldValue;
    },
    'coupon': function(newValue, oldValue) {
      console.log('watch coupon')
      console.log('coupon', newValue)
      // 对于降息提额券，要更新产品数据。
      if (newValue && newValue.limitUpAmount && newValue.limitUpAmount > 0) {
        this.$loading();
        this.queryCustProductInfo('imitUpConpon').then(() => {
          const useScene5Coupon = this.useScene5Coupon
          // 如果使用提额券后没有提额，清除优惠券。
          if (useScene5Coupon && useScene5Coupon.limitIncrease === 0 && useScene5Coupon.interestRelief === 0 && this.coupon.useScene === '5') {
            this.$store.commit('SET_COUPON', {});
            // 这里因为选择优惠券后,会直接更新优惠卷, 还没有从优惠券页面返回，所以这里当前的this.$route.name是couponsList
            if (this.fromName === 'couponsList') {
              this.$newToast('You currently do not meet the conditions for using this coupon');
            }
          } else {
            // 选择提额优惠券后，重新执行产品选择逻辑，触发试算。
            console.log('this.longActive', this.longActive);
            this.chooseType(this.longActive > 0 ? this.longActive : this.proIndex);
          }
          this.$hideLoading();
        }).catch(() => {
          this.$hideLoading();
        })
      } else if (oldValue && oldValue.limitUpAmount && oldValue.limitUpAmount > 0) {
        // 从降息提额券切换成其他券，也要重新更新产品数据
        console.log('imitUpConponRemove')
        this.queryCustProductInfo('imitUpConponRemove').then(() => {
          // 重新执行产品选择逻辑，触发试算。
          this.chooseType(this.longActive > 0 ? this.longActive : this.proIndex);
        })
      }
      // 当前存在优惠券，之前不存在或者是别的优惠券
      if (newValue && newValue.couponId) {
        if (newValue.couponId !== (oldValue && oldValue.couponId)) {
          // 重新试算
          this.startLoanCalculate();
        }
      } else{
        // 当前优惠券不存在，但之前选择过优惠券，则重新试算
        if (oldValue && oldValue.couponId) {
          this.startLoanCalculate();
        }
      }
    },
    'bankIndex': function(newValue, oldValue) {
      console.log('bankIndex', newValue, oldValue);
    },
    showAddPersonalInformation: function(newValue) {
      if (newValue) {
        this.withdrawReport('withdraw_addinfo_popup_view');
      }
    }
  }
}
